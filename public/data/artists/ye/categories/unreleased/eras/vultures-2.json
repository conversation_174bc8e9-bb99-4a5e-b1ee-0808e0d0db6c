{"id": "vultures-2", "name": "VULTURES 2", "description": "VULTURES 2 was announced alongside 2 other volumes of VULTURES, and was meant to release on March 8th, 2024, and then May 3rd, 2024. This album would've became the first release exclusive to the YZYAPP. The cover shows <PERSON> $ign holding a portrait of his brother <PERSON>, who is currently in prison. After the album failed to drop on May 3rd, 2024, the direction of the project completely shifted, and was to be censored. The album failed to drop August 2nd and then dropped August 3rd with questionable mixing/songs.", "backgroundColor": "rgb(31, 33, 31)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FafB9lMOcNhPjLnYeuTDIJ7SvGxpFrB_8cwiJsksKWc6siYz_w2_H4MdBzocx9nZG0dUdReflur0bzv2JRXjHLthyE-ZZae4UY4a3oKI35BT4U29Ttu_MnNonkIWcF?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "believer", "name": "BELIEVER [V4]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: believer updated loop\nYseult reference track. Unknown when made, but the instrumental is similar to ones used on versions with Ty Dolla $ign, placing it after the original Yseult version.", "length": "88.16", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/04dfdefeb79fede466e37c6c85883850", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/04dfdefeb79fede466e37c6c85883850\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: believer updated loop\\nYseult reference track. Unknown when made, but the instrumental is similar to ones used on versions with Ty Dolla $ign, placing it after the original Yseult version.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3b885242a1869803cfeb11a59ca1d297\", \"url\": \"https://api.pillowcase.su/api/download/3b885242a1869803cfeb11a59ca1d297\", \"size\": \"1.87 MB\", \"duration\": 88.16}", "aliases": [], "size": "1.87 MB"}, {"id": "believer-2", "name": "BELIEVER [V5]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: believer <PERSON><PERSON>\nHas more <PERSON> harmony vocals and no Ye.", "length": "87.25", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/197ad754e30821a5975f2e32a474288f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/197ad754e30821a5975f2e32a474288f\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V5]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: believer R1\\nHas more Ty harmony vocals and no Ye.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e406b27a0f75ac3475fc63b0d43d4d54\", \"url\": \"https://api.pillowcase.su/api/download/e406b27a0f75ac3475fc63b0d43d4d54\", \"size\": \"1.85 MB\", \"duration\": 87.25}", "aliases": [], "size": "1.85 MB"}, {"id": "believer-3", "name": "BELIEVER [V6]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: believer R2 -\nEarliest known version that features rough <PERSON> vocals. Unknown when this is dated, but most likely before March 9th. Original snippet leaked August 8th, 2024.", "length": "174.46", "fileDate": 17290368, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c3fc5e210336f9da1c303ba1a1ecf44f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3fc5e210336f9da1c303ba1a1ecf44f\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V6]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: believer R2 -\\nEarliest known version that features rough Ye vocals. Unknown when this is dated, but most likely before March 9th. Original snippet leaked August 8th, 2024.\", \"date\": 17290368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b26f337adb45a45849d39a7621450f3f\", \"url\": \"https://api.pillowcase.su/api/download/b26f337adb45a45849d39a7621450f3f\", \"size\": \"3.25 MB\", \"duration\": 174.46}", "aliases": [], "size": "3.25 MB"}, {"id": "believer-4", "name": "BELIEVER [V8]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast", "JPEGMAFIA"], "notes": "OG Filename: Believer Fre$h_02\nFRE$H reference traack. Shares the same instrumental as the LA listening party version.", "length": "188.99", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1893a43ef9d9ccb2073c9b07ccb99bb5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1893a43ef9d9ccb2073c9b07ccb99bb5\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V8]\", \"artists\": \"(ref. FRE$H) (prod. <PERSON>, BoogzDaBeast & JPEGMAFIA)\", \"description\": \"OG Filename: Believer Fre$h_02\\nFRE$H reference traack. Shares the same instrumental as the LA listening party version.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f820e0fb60edeb612a4da59fd6f6bccd\", \"url\": \"https://api.pillowcase.su/api/download/f820e0fb60edeb612a4da59fd6f6bccd\", \"size\": \"3.48 MB\", \"duration\": 188.99}", "aliases": [], "size": "3.48 MB"}, {"id": "believer-5", "name": "BELIEVER [V9]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast", "", "JPEGMAFIA"], "notes": "<PERSON> played during the private LA listening party for VULTURES 2 on March 9th, 2024. Features solo Ty <PERSON> $ign vocals and an intro sampling a Charleston White interview. <PERSON> added JPEGMAFIA drums and <PERSON><PERSON><PERSON> chops onto <PERSON>'s loop. Very similar to what was released on the digital deluxe.", "length": "86.8", "fileDate": 17099424, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/468be0d6758954f8b8b66c51dd09d362", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/468be0d6758954f8b8b66c51dd09d362\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V9]\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, & JPEGMAFIA)\", \"description\": \"<PERSON> played during the private LA listening party for VULTURES 2 on March 9th, 2024. Features solo <PERSON>ign vocals and an intro sampling a Charleston White interview. Has added JPEGMAFIA drums and <PERSON><PERSON><PERSON> chops onto <PERSON>'s loop. Very similar to what was released on the digital deluxe.\", \"date\": 17099424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f5e259f7bae0d189ec13a98521bedf94\", \"url\": \"https://api.pillowcase.su/api/download/f5e259f7bae0d189ec13a98521bedf94\", \"size\": \"2.11 MB\", \"duration\": 86.8}", "aliases": [], "size": "2.11 MB"}, {"id": "believer-6", "name": "🗑️ BELIEVER [V11]", "artists": [], "producers": ["<PERSON>", "star boy"], "notes": "OG Filename: BELIEVER_3.10.24 STARBOY_SHORTER\nPlayed during the Phoenix listening party. Has different sequencing and production. According to <PERSON><PERSON> the production changes were likely only meant for the Phoenix LP. <PERSON>'s vocals sound like he's congested. star boy came out to say that he did not clear this for <PERSON>.", "length": "72.79", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c19a28bc791c93e09186aff3c9bc4e15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c19a28bc791c93e09186aff3c9bc4e15\", \"key\": \"BELIEVER\", \"title\": \"\\ud83d\\uddd1\\ufe0f BELIEVER [V11]\", \"artists\": \"(prod. <PERSON> & star boy)\", \"description\": \"OG Filename: BELIEVER_3.10.24 STARBOY_SHORTER\\nPlayed during the Phoenix listening party. Has different sequencing and production. According to <PERSON><PERSON> the production changes were likely only meant for the Phoenix LP. <PERSON>'s vocals sound like he's congested. star boy came out to say that he did not clear this for <PERSON>.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0543b0cc81514456da691f327470eb2d\", \"url\": \"https://api.pillowcase.su/api/download/0543b0cc81514456da691f327470eb2d\", \"size\": \"1.62 MB\", \"duration\": 72.79}", "aliases": [], "size": "1.62 MB"}, {"id": "believer-7", "name": "BELIEVER [V12]", "artists": [], "producers": ["<PERSON>", "JPEGMAFIA"], "notes": "OG Filename: believer qm v1\nReference track for \"Believer\" done by <PERSON>. Original snippet leaked October 19th, 2024. Shares the same instrumental as the LA listening party version without the <PERSON><PERSON><PERSON> chops.", "length": "87.94", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5ea287d2620dac2077ba9079fad1b8f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ea287d2620dac2077ba9079fad1b8f8\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V12]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & JPEGMAFIA)\", \"description\": \"OG Filename: believer qm v1\\nReference track for \\\"Believer\\\" done by <PERSON>. Original snippet leaked October 19th, 2024. Shares the same instrumental as the LA listening party version without the Boogz chops.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dc418395bed8251b0b6366ecbe40fa43\", \"url\": \"https://api.pillowcase.su/api/download/dc418395bed8251b0b6366ecbe40fa43\", \"size\": \"1.87 MB\", \"duration\": 87.94}", "aliases": [], "size": "1.87 MB"}, {"id": "dead", "name": "DEAD [V12]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob", "Wheezy"], "notes": "Played during the private LA listening party for VULTURES 2 on March 9th, 2024. The controversial \"Jews\" line said by <PERSON> at the end has been cut out in this version. Original snippet was posted on <PERSON><PERSON> Taylor's Instagram February 20th, 2024.", "length": "237.22", "fileDate": 17099424, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/7445f9e2d01e61dafc64422961cfff74", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7445f9e2d01e61dafc64422961cfff74\", \"key\": \"DEAD\", \"title\": \"DEAD [V12]\", \"artists\": \"(feat. <PERSON> <PERSON>) (prod. <PERSON><PERSON> Jacob & Wheezy)\", \"description\": \"Played during the private LA listening party for VULTURES 2 on March 9th, 2024. The controversial \\\"Jews\\\" line said by <PERSON> at the end has been cut out in this version. Original snippet was posted on <PERSON><PERSON> Taylor's Instagram February 20th, 2024.\", \"date\": 17099424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"43eacba510d59a2eedb780d3087c19c0\", \"url\": \"https://api.pillowcase.su/api/download/43eacba510d59a2eedb780d3087c19c0\", \"size\": \"4.52 MB\", \"duration\": 237.22}", "aliases": [], "size": "4.52 MB"}, {"id": "dead-9", "name": "DEAD [V13]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob", "Wheezy"], "notes": "OG Filename: dead 3.10.24 show-1\nVersion of \"Dead\" played at the Phoenix LP, with the \"You know I'm crazy but you crazy glued to me\" line repeating rather than cutting off immediately.", "length": "290.22", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/cd98fe0b98190501c13ead109d8b0af1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cd98fe0b98190501c13ead109d8b0af1\", \"key\": \"DEAD\", \"title\": \"DEAD [V13]\", \"artists\": \"(feat. <PERSON> <PERSON> Lil <PERSON>) (prod. AT<PERSON> Jacob & Wheezy)\", \"description\": \"OG Filename: dead 3.10.24 show-1\\nVersion of \\\"Dead\\\" played at the Phoenix LP, with the \\\"You know I'm crazy but you crazy glued to me\\\" line repeating rather than cutting off immediately.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"63438292f32e18884c34d979f79f3430\", \"url\": \"https://api.pillowcase.su/api/download/63438292f32e18884c34d979f79f3430\", \"size\": \"5.1 MB\", \"duration\": 290.22}", "aliases": [], "size": "5.1 MB"}, {"id": "fear", "name": "FEAR [V2]", "artists": [], "producers": ["<PERSON>", "88-<PERSON>"], "notes": "OG Filename: FEAR X MOOSE REF 1\nYoung Moose reference track. Has a different take of <PERSON> doing the \"I might have to\" line. <PERSON> would use this ref to create the April 29th, 2024 version of the song.", "length": "142.13", "fileDate": 17375040, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/911e4b67604cf2997b02d309efdb3380", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/911e4b67604cf2997b02d309efdb3380\", \"key\": \"FEAR\", \"title\": \"FEAR [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & 88-Keys)\", \"description\": \"OG Filename: FEAR X MOOSE REF 1\\nYoung Moose reference track. Has a different take of <PERSON> doing the \\\"I might have to\\\" line. <PERSON> would use this ref to create the April 29th, 2024 version of the song.\", \"date\": 17375040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"736b2e29a444ba1f1ef45377ded53006\", \"url\": \"https://api.pillowcase.su/api/download/736b2e29a444ba1f1ef45377ded53006\", \"size\": \"2.73 MB\", \"duration\": 142.13}", "aliases": [], "size": "2.73 MB"}, {"id": "fear-11", "name": "⭐ FEAR [V3]", "artists": [], "producers": ["<PERSON>", "88-<PERSON>"], "notes": "OG Filename: 042924 FEAR 81BPM FSHARPMINOR\nFully finished song recorded in April 2024. <PERSON> <PERSON> \"Fear\" by <PERSON><PERSON> <PERSON><PERSON> snippet leaked October 29th, 2024. <PERSON> later on found about a potential groupbuy for this song, and voiced his anger for buying unreleased music, which then caused the groupbuy to not happen. Leaked anyways after a separate groupbuy.", "length": "124.44", "fileDate": 17375040, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/efb01dd80e192ccd51157cf7e45083a2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/efb01dd80e192ccd51157cf7e45083a2\", \"key\": \"FEAR\", \"title\": \"\\u2b50 FEAR [V3]\", \"artists\": \"(prod. <PERSON> & 88-<PERSON>)\", \"description\": \"OG Filename: 042924 FEAR 81BPM FSHARPMINOR\\nFully finished song recorded in April 2024. Solo <PERSON><PERSON> \\\"Fear\\\" by <PERSON><PERSON> <PERSON> snippet leaked October 29th, 2024. <PERSON> later on found about a potential groupbuy for this song, and voiced his anger for buying unreleased music, which then caused the groupbuy to not happen. Leaked anyways after a separate groupbuy.\", \"date\": 17375040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cf29bce6b98c1406cd736e252f49c7c4\", \"url\": \"https://api.pillowcase.su/api/download/cf29bce6b98c1406cd736e252f49c7c4\", \"size\": \"2.45 MB\", \"duration\": 124.44}", "aliases": [], "size": "2.45 MB"}, {"id": "field-trip", "name": "FIELD TRIP [V12]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Played during the private LA listening party for VULTURES 2 on March 9th, 2024. No Ye verse was played however it was cut off early. Same version played in Phoenix. It's been said by <PERSON><PERSON> that a Ye verse does exist on the beatswitch of the song, but that it is a freestyle.", "length": "183.48", "fileDate": 17099424, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/26d8107488de976722d1df045d25faeb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/26d8107488de976722d1df045d25faeb\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V12]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Played during the private LA listening party for VULTURES 2 on March 9th, 2024. No Ye verse was played however it was cut off early. Same version played in Phoenix. It's been said by <PERSON><PERSON> that a Ye verse does exist on the beatswitch of the song, but that it is a freestyle.\", \"date\": 17099424, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e85308847f8bf3f481f413e269196e26\", \"url\": \"https://api.pillowcase.su/api/download/e85308847f8bf3f481f413e269196e26\", \"size\": \"3.66 MB\", \"duration\": 183.48}", "aliases": [], "size": "3.66 MB"}, {"id": "field-trip-13", "name": "FIELD TRIP [V13]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Ojivolta", "Wheezy", "<PERSON><PERSON>"], "notes": "OG Filename: FIELD TRIP_3.10.24_OV W DRUMS\nPlayed at the Phoenix listening party on March 10th, 2024 in full.", "length": "192.86", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5087b6842b667df0200d86303a4a400b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5087b6842b667df0200d86303a4a400b\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V13]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. Ojivolta, <PERSON><PERSON>zy & <PERSON><PERSON>)\", \"description\": \"OG Filename: FIELD TRIP_3.10.24_OV W DRUMS\\nPlayed at the Phoenix listening party on March 10th, 2024 in full.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"df70ff9bb6e19714aeab347fc9cb5ebc\", \"url\": \"https://api.pillowcase.su/api/download/df70ff9bb6e19714aeab347fc9cb5ebc\", \"size\": \"3.54 MB\", \"duration\": 192.86}", "aliases": [], "size": "3.54 MB"}, {"id": "field-trip-14", "name": "FIELD TRIP [V14]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kodak Black"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version played at the Chase Center listening event and at Rolling Loud. Has an added verse from <PERSON><PERSON>, and a second <PERSON><PERSON> verse which was cut early.", "length": "245.13", "fileDate": 17102016, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/20ac2d8bb17d84d24242a9c0640c8fd1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20ac2d8bb17d84d24242a9c0640c8fd1\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Version played at the Chase Center listening event and at Rolling Loud. Has an added verse from <PERSON><PERSON> Black, and a second <PERSON><PERSON> verse which was cut early.\", \"date\": 17102016, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b94e7155a38983abc685f0eb6acc9b13\", \"url\": \"https://api.pillowcase.su/api/download/b94e7155a38983abc685f0eb6acc9b13\", \"size\": \"4.64 MB\", \"duration\": 245.13}", "aliases": [], "size": "4.64 MB"}, {"id": "field-trip-15", "name": "FIELD TRIP [V15]", "artists": ["<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version with only <PERSON> and <PERSON>. Played during <PERSON>'s set at Rolling Loud California 2024.", "length": "71.96", "fileDate": 17106336, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/fc91f2f1ba672b9f9e2b2dc711de1ba9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fc91f2f1ba672b9f9e2b2dc711de1ba9\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V15]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Version with only <PERSON> and <PERSON>. Played during <PERSON>'s set at Rolling Loud California 2024.\", \"date\": 17106336, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"55995ad0750719ceaa265889d1c10ab2\", \"url\": \"https://api.pillowcase.su/api/download/55995ad0750719ceaa265889d1c10ab2\", \"size\": \"1.87 MB\", \"duration\": 71.96}", "aliases": [], "size": "1.87 MB"}, {"id": "field-trip-16", "name": "FIELD TRIP [V16]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kodak Black"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version played at Ty Dolla $ign's birthday event on April 13th, 2024. <PERSON><PERSON>'s verse is now placed over the beatswitch section of the song. It's unknown if the song has Ye vocals at this point.", "length": "", "fileDate": 17129664, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5690917186824a0bebeeab407920fc14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5690917186824a0bebeeab407920fc14\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V16]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Version played at Ty Dolla $ign's birthday event on April 13th, 2024. <PERSON><PERSON>'s verse is now placed over the beatswitch section of the song. It's unknown if the song has Ye vocals at this point.\", \"date\": 17129664, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "field-trip-17", "name": "FIELD TRIP [V17]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version played in an Abu Dhabi event. Has additional adlibs from <PERSON><PERSON> during the second <PERSON> hook. It's unknown if <PERSON><PERSON> is still on the song. Other changes are unknown.", "length": "73.67", "fileDate": 17141760, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/862307ebed3525aee0703a457f81fe0f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/862307ebed3525aee0703a457f81fe0f\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V17]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Version played in an Abu Dhabi event. Has additional adlibs from <PERSON><PERSON> during the second Don hook. It's unknown if <PERSON><PERSON> is still on the song. Other changes are unknown.\", \"date\": 17141760, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"64cd6fbb7dc7465c3b708add7cc13e8e\", \"url\": \"https://api.pillowcase.su/api/download/64cd6fbb7dc7465c3b708add7cc13e8e\", \"size\": \"1.9 MB\", \"duration\": 73.67}", "aliases": [], "size": "1.9 MB"}, {"id": "field-trip-18", "name": "FIELD TRIP [V18]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "Real Hubi", "SHDØW", "<PERSON><PERSON>", "<PERSON>", "Timbaland", "Ojivolta", "The Legendary Traxster"], "notes": "Version played by <PERSON><PERSON><PERSON><PERSON> on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.", "length": "85.18", "fileDate": 17170272, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/613d1767cb489cc1245eeae900db79f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/613d1767cb489cc1245eeae900db79f5\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>\\u00d8<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Ojivolta & The Legendary Traxster)\", \"description\": \"Version played by SHD\\u00d8W on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.\", \"date\": 17170272, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a70c23b4dd5a068eb689e71b2a9d8381\", \"url\": \"https://api.pillowcase.su/api/download/a70c23b4dd5a068eb689e71b2a9d8381\", \"size\": \"2.08 MB\", \"duration\": 85.18}", "aliases": [], "size": "2.08 MB"}, {"id": "field-trip-19", "name": "FIELD TRIP [V18]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "Real Hubi", "SHDØW", "<PERSON><PERSON>", "<PERSON>", "Timbaland", "Ojivolta", "The Legendary Traxster"], "notes": "Version played by <PERSON><PERSON><PERSON><PERSON> on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.", "length": "20.86", "fileDate": 17170272, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5b2856226c22af63592c20aff98c9fde", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5b2856226c22af63592c20aff98c9fde\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod<PERSON> <PERSON>, <PERSON>, <PERSON><PERSON>\\u00d8<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Ojivolta & The Legendary Traxster)\", \"description\": \"Version played by SHD\\u00d8W on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.\", \"date\": 17170272, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4dac33c6c23e4324744ae1635a49ca2c\", \"url\": \"https://api.pillowcase.su/api/download/4dac33c6c23e4324744ae1635a49ca2c\", \"size\": \"1.05 MB\", \"duration\": 20.86}", "aliases": [], "size": "1.05 MB"}, {"id": "field-trip-20", "name": "FIELD TRIP [V19]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "taydex"], "notes": "Has production from Wes Singerman & taydex. Snippet leaked January 1st, 2025.", "length": "11.7", "fileDate": 17356896, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5ea223384984049e43b49aa19951161b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ea223384984049e43b49aa19951161b\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V19]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON> & taydex)\", \"description\": \"Has production from Wes <PERSON>man & taydex. Snippet leaked January 1st, 2025.\", \"date\": 17356896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"93da3c7a2cfa603e46c844cac22db939\", \"url\": \"https://api.pillowcase.su/api/download/93da3c7a2cfa603e46c844cac22db939\", \"size\": \"645 kB\", \"duration\": 11.7}", "aliases": [], "size": "645 kB"}, {"id": "field-trip-21", "name": "FIELD TRIP [V21]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "???"], "producers": ["<PERSON><PERSON>"], "notes": "Version of \"Field Trip\" with production from <PERSON><PERSON>. Has multiple beat switches and additional adlibs. Snippet leaked November 18th, 2024.", "length": "15.88", "fileDate": 17318880, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1f275b0a7fc67ef7db0ff8bf42bd058f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f275b0a7fc67ef7db0ff8bf42bd058f\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V21]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & ???) (prod. <PERSON>)\", \"description\": \"Version of \\\"Field Trip\\\" with production from Cruza. Has multiple beat switches and additional adlibs. Snippet leaked November 18th, 2024.\", \"date\": 17318880, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ee833c813c9c372c733b56edc86d9bf0\", \"url\": \"https://api.pillowcase.su/api/download/ee833c813c9c372c733b56edc86d9bf0\", \"size\": \"712 kB\", \"duration\": 15.88}", "aliases": [], "size": "712 kB"}, {"id": "field-trip-22", "name": "FIELD TRIP [V22]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON>"], "notes": "Another alternate version of \"Field Trip\" with production from Cruza. Snippet leaked December 13th, 2024.", "length": "5.9", "fileDate": 17340480, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/000f7c5c004b78821178e7f04cc3763f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/000f7c5c004b78821178e7f04cc3763f\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V22]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>)\", \"description\": \"Another alternate version of \\\"Field Trip\\\" with production from Cruza. Snippet leaked December 13th, 2024.\", \"date\": 17340480, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3d72dd91e2356b05c4eb227f12c7b185\", \"url\": \"https://api.pillowcase.su/api/download/3d72dd91e2356b05c4eb227f12c7b185\", \"size\": \"552 kB\", \"duration\": 5.9}", "aliases": [], "size": "552 kB"}, {"id": "bleed-it", "name": "<PERSON> the Kid - BLEED IT [V2]", "artists": ["Kanye West"], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: Bleed It\nHas 2 verses and a hook done by <PERSON>, as well as mumbly iPhone vocals from <PERSON><PERSON> Some of <PERSON>'s lyrics would be reused by <PERSON> in the March 20th version.", "length": "140.81", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/58f7e520ef58f772fed5f51a226b3259", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/58f7e520ef58f772fed5f51a226b3259\", \"key\": \"BLEED IT\", \"title\": \"<PERSON> the Kid - BLEED IT [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Ojivolta)\", \"aliases\": [\"FRIED\"], \"description\": \"OG Filename: Bleed It\\nHas 2 verses and a hook done by <PERSON>, as well as mumbly iPhone vocals from <PERSON><PERSON> Some of <PERSON>'s lyrics would be reused by <PERSON> in the March 20th version.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d83db8fa203404d8774cce4c0ee7b956\", \"url\": \"https://api.pillowcase.su/api/download/d83db8fa203404d8774cce4c0ee7b956\", \"size\": \"2.71 MB\", \"duration\": 140.81}", "aliases": ["FRIED"], "size": "2.71 MB"}, {"id": "bleed", "name": "<PERSON> the Kid - BLEED [V3]", "artists": [], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "OG Filename: BLEED 150BPM LABCOOK\nHas extra TheLabCook production.", "length": "166.45", "fileDate": 17305920, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d1e17f95248eb0f8db2be0bb8c053446", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1e17f95248eb0f8db2be0bb8c053446\", \"key\": \"BLEED\", \"title\": \"<PERSON> the Kid - BLEED [V3]\", \"artists\": \"(prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\", \"FRIED\"], \"description\": \"OG Filename: BLEED 150BPM LABCOOK\\nHas extra TheLabCook production.\", \"date\": 17305920, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3aedab24fab400e82be49573a2a56ed0\", \"url\": \"https://api.pillowcase.su/api/download/3aedab24fab400e82be49573a2a56ed0\", \"size\": \"3.12 MB\", \"duration\": 166.45}", "aliases": ["BLEED IT", "FRIED"], "size": "3.12 MB"}, {"id": "fried", "name": "FRIED [V4]", "artists": [], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "<PERSON> Ye freestyle over the original production, likely when he took the song for himself. Snippet leaked November 4th, 2024.", "length": "7.05", "fileDate": 17306784, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/894a01561c64aef4ff3980b6072499e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/894a01561c64aef4ff3980b6072499e1\", \"key\": \"FRIED\", \"title\": \"FRIED [V4]\", \"artists\": \"(prod. <PERSON> Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Rough Ye freestyle over the original production, likely when he took the song for himself. Snippet leaked November 4th, 2024.\", \"date\": 17306784, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6894e4c32767a15907d433e3d9681fc8\", \"url\": \"https://api.pillowcase.su/api/download/6894e4c32767a15907d433e3d9681fc8\", \"size\": \"571 kB\", \"duration\": 7.05}", "aliases": ["BLEED IT"], "size": "571 kB"}, {"id": "fried-26", "name": "FRIED [V5]", "artists": [], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "OG Filename: FRIED hook idea\nBump J reference track, made before The Hooligans were added onto the song.", "length": "12.5", "fileDate": 17309376, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/54d549b4d7d095f49ed746f334bb701f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/54d549b4d7d095f49ed746f334bb701f\", \"key\": \"FRIED\", \"title\": \"FRIED [V5]\", \"artists\": \"(ref. Bump J) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"OG Filename: FRIED hook idea\\nBump J reference track, made before The Hooligans were added onto the song.\", \"date\": 17309376, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"462ccf39f2fceff6d6b924a83f5a2cc0\", \"url\": \"https://api.pillowcase.su/api/download/462ccf39f2fceff6d6b924a83f5a2cc0\", \"size\": \"658 kB\", \"duration\": 12.5}", "aliases": ["BLEED IT"], "size": "658 kB"}, {"id": "fried-27", "name": "FRIED [V6]", "artists": [], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "<PERSON><PERSON> ref for \"Fried\". Snippet leaked November 4th, 2024.", "length": "6.91", "fileDate": 17306784, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/07f366abf2b40e46cdbb77aee9f68933", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/07f366abf2b40e46cdbb77aee9f68933\", \"key\": \"FRIED\", \"title\": \"FRIED [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Ant Clemons ref for \\\"Fried\\\". Snippet leaked November 4th, 2024.\", \"date\": 17306784, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1898645dc8752d337b6b91cb2e94b2fc\", \"url\": \"https://api.pillowcase.su/api/download/1898645dc8752d337b6b91cb2e94b2fc\", \"size\": \"569 kB\", \"duration\": 6.91}", "aliases": ["BLEED IT"], "size": "569 kB"}, {"id": "bleed-it-28", "name": "BLEED IT [V7]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: BLEED IT - 3.6.24 - Hooligans beat ref s<PERSON><PERSON> made for the song \"Fried\". Samples an interaction that occurred between <PERSON> & a member of the paparazzi that happened just before the release of VULTURES 1. Leaked randomly on August 6th, 2024, but turned out to be only partial - later leaked in full.", "length": "28.85", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/0b62b3ee5f10a1b44d5a56bcbe2d27f6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b62b3ee5f10a1b44d5a56bcbe2d27f6\", \"key\": \"BLEED IT\", \"title\": \"BLEED IT [V7]\", \"artists\": \"(feat. The Hooligans) (prod. Digital Nas & Ojivolta)\", \"aliases\": [\"FRIED\"], \"description\": \"OG Filename: BLEED IT - 3.6.24 - Hooligans beat ref skjit\\nSkit made for the song \\\"Fried\\\". Samples an interaction that occurred between <PERSON> & a member of the paparazzi that happened just before the release of VULTURES 1. Leaked randomly on August 6th, 2024, but turned out to be only partial - later leaked in full.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"83f883509d62da089219092975469080\", \"url\": \"https://api.pillowcase.su/api/download/83f883509d62da089219092975469080\", \"size\": \"920 kB\", \"duration\": 28.85}", "aliases": ["FRIED"], "size": "920 kB"}, {"id": "fried-29", "name": "FRIED [V8]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "OG Filename: FRIED - ye verse v1\nHas mumble and alternate Ye vocals, and rough production.", "length": "108.9", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d891bdf8d67ffc83d0223f3d380c8abe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d891bdf8d67ffc83d0223f3d380c8abe\", \"key\": \"FRIED\", \"title\": \"FRIED [V8]\", \"artists\": \"(feat. <PERSON> Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"OG Filename: FRIED - ye verse v1\\nHas mumble and alternate Ye vocals, and rough production.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"832e1f88e8272c1207adb019c70e46cb\", \"url\": \"https://api.pillowcase.su/api/download/832e1f88e8272c1207adb019c70e46cb\", \"size\": \"2.2 MB\", \"duration\": 108.9}", "aliases": ["BLEED IT"], "size": "2.2 MB"}, {"id": "fried-30", "name": "FRIED [V9]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "Version played at the LA private listening event. Has different sequencing.", "length": "96.84", "fileDate": 17099424, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5e298e39a54c5a17ef40b42745f69989", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e298e39a54c5a17ef40b42745f69989\", \"key\": \"FRIED\", \"title\": \"FRIED [V9]\", \"artists\": \"(feat. <PERSON> Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Version played at the LA private listening event. Has different sequencing.\", \"date\": 17099424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7f56a13a875417b938cb576172ae51da\", \"url\": \"https://api.pillowcase.su/api/download/7f56a13a875417b938cb576172ae51da\", \"size\": \"2.27 MB\", \"duration\": 96.84}", "aliases": ["BLEED IT"], "size": "2.27 MB"}, {"id": "fried-31", "name": "FRIED [V10]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "OG Filename: FRIED_3.10.24\nVersion played at the Phoenix event.", "length": "128.06", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/9416903084e09761ca59e4cebcefafec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9416903084e09761ca59e4cebcefafec\", \"key\": \"FRIED\", \"title\": \"FRIED [V10]\", \"artists\": \"(feat. The Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"OG Filename: FRIED_3.10.24\\nVersion played at the Phoenix event.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"57f78fd287fbabdb1f6711ae3ac654dc\", \"url\": \"https://api.pillowcase.su/api/download/57f78fd287fbabdb1f6711ae3ac654dc\", \"size\": \"2.51 MB\", \"duration\": 128.06}", "aliases": ["BLEED IT"], "size": "2.51 MB"}, {"id": "fried-32", "name": "✨ FRIED [V11]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "OG Filename: FRIED_ty$ x ye\nVersion with different drums and alternate Ty lines (that are mostly from the Rich The Kid version) and no actual verse from him, instead he goes back and forth with <PERSON> until the outro. <PERSON> also harmonises with The Hooligans at the end.", "length": "153.6", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/30599299db2a58d1faf67e0028388975", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/30599299db2a58d1faf67e0028388975\", \"key\": \"FRIED\", \"title\": \"\\u2728 FRIED [V11]\", \"artists\": \"(feat. The Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"OG Filename: FRIED_ty$ x ye\\nVersion with different drums and alternate Ty lines (that are mostly from the Rich The Kid version) and no actual verse from him, instead he goes back and forth with <PERSON> until the outro. <PERSON> also harmonises with The Hooligans at the end.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ee088f659eb271a91b8a4d70c7ec00df\", \"url\": \"https://api.pillowcase.su/api/download/ee088f659eb271a91b8a4d70c7ec00df\", \"size\": \"2.92 MB\", \"duration\": 153.6}", "aliases": ["BLEED IT"], "size": "2.92 MB"}, {"id": "fried-33", "name": "FRIED [V12]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "Version of \"Fried\" played in an Abu Dhabi event. See<PERSON><PERSON> has added adlibs.", "length": "104.88", "fileDate": 17141760, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/a6a8ebe73a63741f41ed3424b46bd637", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6a8ebe73a63741f41ed3424b46bd637\", \"key\": \"FRIED\", \"title\": \"FRIED [V12]\", \"artists\": \"(feat. The Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Version of \\\"Fried\\\" played in an Abu Dhabi event. Seemingly has added adlibs.\", \"date\": 17141760, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1a52bd41d94eb6c3cd6ca0204915db6c\", \"url\": \"https://api.pillowcase.su/api/download/1a52bd41d94eb6c3cd6ca0204915db6c\", \"size\": \"2.4 MB\", \"duration\": 104.88}", "aliases": ["BLEED IT"], "size": "2.4 MB"}, {"id": "fukk-sum", "name": "✨ FUKK SUM (Remix) [V6]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo", "<PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "OG Filename: FUKK SUM 3.18.24 ME- F REF 5 ANT\nLater version of the remix with added production and a new feature from <PERSON><PERSON>, replacing a portion of <PERSON><PERSON><PERSON>'s pitched-up vocals. Meant for release on VULTURES 2.", "length": "231.6", "fileDate": 17321472, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c5c95c268b6a75ed41e1881980b4b9e6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c5c95c268b6a75ed41e1881980b4b9e6\", \"key\": \"FUKK SUM (Remix)\", \"title\": \"\\u2728 FUKK SUM (Remix) [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUK SUMN\", \"FUCK SUM\"], \"description\": \"OG Filename: FUKK SUM 3.18.24 ME- F REF 5 ANT\\nLater version of the remix with added production and a new feature from <PERSON><PERSON>, replacing a portion of <PERSON><PERSON><PERSON>'s pitched-up vocals. Meant for release on VULTURES 2.\", \"date\": 17321472, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8833558435ddee5f74b0759bcd725671\", \"url\": \"https://api.pillowcase.su/api/download/8833558435ddee5f74b0759bcd725671\", \"size\": \"4.16 MB\", \"duration\": 231.6}", "aliases": ["Smoking On Junt", "FUK SUMN", "FUCK SUM"], "size": "4.16 MB"}, {"id": "gun-to-my-head", "name": "GUN TO MY HEAD [V9]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "One second of a possibly new version of \"Gun To My Head\" was played during the LA listening party. We don't know about any new changes.", "length": "1.3599999999999999", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/55861c81c066c8a97e5bb2af348bd162", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/55861c81c066c8a97e5bb2af348bd162\", \"key\": \"GUN TO MY HEAD\", \"title\": \"GUN TO MY HEAD [V9]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"One second of a possibly new version of \\\"Gun To My Head\\\" was played during the LA listening party. We don't know about any new changes.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"73e7dd290194872d9aad453dfe49ed98\", \"url\": \"https://api.pillowcase.su/api/download/73e7dd290194872d9aad453dfe49ed98\", \"size\": \"731 kB\", \"duration\": 1.3599999999999999}", "aliases": [], "size": "731 kB"}, {"id": "imagine-dat", "name": "IMAGINE DAT [V8]", "artists": [], "producers": ["Traxster"], "notes": "OG Filename: TRAX MOOSE YE IMAGINE DAT R7\nHas a Young Moose verse, currently unknown if it's intended as a reference or a feature. Leaked after a Joebuy.", "length": "266.76", "fileDate": 17277408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d6ca9e0bedd327affb5e0f4c5c2b8463", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d6ca9e0bedd327affb5e0f4c5c2b8463\", \"key\": \"IMAGINE DAT\", \"title\": \"IMAGINE DAT [V8]\", \"artists\": \"(???. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"BACK N THAT (IMAGINE THAT)\"], \"description\": \"OG Filename: TRAX MOOSE YE IMAGINE DAT R7\\nHas a Young Moose verse, currently unknown if it's intended as a reference or a feature. Leaked after a Joebuy.\", \"date\": 17277408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6fb3b1779bd2f0fb34185e65200a0a78\", \"url\": \"https://api.pillowcase.su/api/download/6fb3b1779bd2f0fb34185e65200a0a78\", \"size\": \"4.73 MB\", \"duration\": 266.76}", "aliases": ["BACK N THAT (IMAGINE THAT)"], "size": "4.73 MB"}, {"id": "vagabond", "name": "VAGABOND [V1]", "artists": ["070 Shake"], "producers": [], "notes": "OG Filename: vagabond 3.10\nPlayed at the Phoenix listening party, as well as the private LA listening party. Has no beat, and only 070 Shake vocals. 070 <PERSON> confirmed in a Discord stage that she never gave the song to <PERSON>, but she did play it for him.", "length": "99.4", "fileDate": 17290368, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/65bfaf6475efb7f3a0ea09068f91fdd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65bfaf6475efb7f3a0ea09068f91fdd7\", \"key\": \"VAGABOND\", \"title\": \"VAGABOND [V1]\", \"artists\": \"(feat. 070 Shake)\", \"aliases\": [\"INTRO\"], \"description\": \"OG Filename: vagabond 3.10\\nPlayed at the Phoenix listening party, as well as the private LA listening party. Has no beat, and only 070 Shake vocals. 070 <PERSON> confirmed in a Discord stage that she never gave the song to <PERSON>, but she did play it for him.\", \"date\": 17290368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3f13313710cdfc278a0d6a6fcd9e6018\", \"url\": \"https://api.pillowcase.su/api/download/3f13313710cdfc278a0d6a6fcd9e6018\", \"size\": \"2.05 MB\", \"duration\": 99.4}", "aliases": ["INTRO"], "size": "2.05 MB"}, {"id": "intro", "name": "🗑️ INTRO [V2]", "artists": ["070 Shake"], "producers": ["88-<PERSON>"], "notes": "OG Filename: intro - 88 drums - v1\n88-Keys drum idea.", "length": "85.71", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/922b408e74a8f9eb7c5fe21397d8b838", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/922b408e74a8f9eb7c5fe21397d8b838\", \"key\": \"INTRO\", \"title\": \"\\ud83d\\uddd1\\ufe0f INTRO [V2]\", \"artists\": \"(feat. 070 Shake) (prod. 88-Keys)\", \"aliases\": [\"VAGABOND\"], \"description\": \"OG Filename: intro - 88 drums - v1\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4f41db0d72b50f1f42870a3c02dc156d\", \"url\": \"https://api.pillowcase.su/api/download/4f41db0d72b50f1f42870a3c02dc156d\", \"size\": \"1.83 MB\", \"duration\": 85.71}", "aliases": ["VAGABOND"], "size": "1.83 MB"}, {"id": "intro-39", "name": "INTRO [V3]", "artists": ["070 Shake"], "producers": ["88-<PERSON>"], "notes": "OG Filename: intro - 88 drums - v2\n88-Keys drum idea.", "length": "42.89", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/55cc663b27dbc93e30571c81a62bdbf5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/55cc663b27dbc93e30571c81a62bdbf5\", \"key\": \"INTRO\", \"title\": \"INTRO [V3]\", \"artists\": \"(feat. 070 Shake) (prod. 88-Keys)\", \"aliases\": [\"VAGABOND\"], \"description\": \"OG Filename: intro - 88 drums - v2\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9cfcb700d0c381f082f607184de08129\", \"url\": \"https://api.pillowcase.su/api/download/9cfcb700d0c381f082f607184de08129\", \"size\": \"1.14 MB\", \"duration\": 42.89}", "aliases": ["VAGABOND"], "size": "1.14 MB"}, {"id": "intro-40", "name": "INTRO [V4]", "artists": ["070 Shake"], "producers": ["88-<PERSON>"], "notes": "OG Filename: intro - 88 drums - v3\n88-Keys drum idea.", "length": "42.89", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/fae907df0f922383cf48cbd60ee52d5d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fae907df0f922383cf48cbd60ee52d5d\", \"key\": \"INTRO\", \"title\": \"INTRO [V4]\", \"artists\": \"(feat. 070 Shake) (prod. 88-Keys)\", \"aliases\": [\"VAGABOND\"], \"description\": \"OG Filename: intro - 88 drums - v3\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7ad64a7219e70db967f7f343d4dadb92\", \"url\": \"https://api.pillowcase.su/api/download/7ad64a7219e70db967f7f343d4dadb92\", \"size\": \"1.14 MB\", \"duration\": 42.89}", "aliases": ["VAGABOND"], "size": "1.14 MB"}, {"id": "intro-41", "name": "INTRO [V5]", "artists": ["070 Shake"], "producers": ["88-<PERSON>"], "notes": "OG Filename: intro - 88 drums - v4\n88-Keys drum idea.", "length": "42.89", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5e17d75f8f01fd4a0f6ed071e90158e0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e17d75f8f01fd4a0f6ed071e90158e0\", \"key\": \"INTRO\", \"title\": \"INTRO [V5]\", \"artists\": \"(feat. 070 Shake) (prod. 88-Keys)\", \"aliases\": [\"VAGABOND\"], \"description\": \"OG Filename: intro - 88 drums - v4\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8481aed8483dda0e851bf456695f0d8d\", \"url\": \"https://api.pillowcase.su/api/download/8481aed8483dda0e851bf456695f0d8d\", \"size\": \"1.14 MB\", \"duration\": 42.89}", "aliases": ["VAGABOND"], "size": "1.14 MB"}, {"id": "intro-42", "name": "INTRO [V6]", "artists": ["070 Shake"], "producers": ["88-<PERSON>"], "notes": "OG Filename: intro - 88 drums - v5\n88-Keys drum idea.", "length": "42.89", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/e75ab539c073ec593f992173c067eb16", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e75ab539c073ec593f992173c067eb16\", \"key\": \"INTRO\", \"title\": \"INTRO [V6]\", \"artists\": \"(feat. 070 Shake) (prod. 88-Keys)\", \"aliases\": [\"VAGABOND\"], \"description\": \"OG Filename: intro - 88 drums - v5\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1c41e5f900c104eabcf39eb845049221\", \"url\": \"https://api.pillowcase.su/api/download/1c41e5f900c104eabcf39eb845049221\", \"size\": \"1.14 MB\", \"duration\": 42.89}", "aliases": ["VAGABOND"], "size": "1.14 MB"}, {"id": "lifestyle", "name": "LIFESTYLE [V6]", "artists": [], "producers": ["<PERSON>", "FnZ", "London on da Track"], "notes": "Heard in the AI extracted vocal bleed of the April 29th, 2024 copy of \"Lifestyle\". Most likely just a different version of the song with faster trap drums that were in earlier versions and has <PERSON> doing vocals on the \"Husband\" part. <PERSON> provided is an inverted version, the part with faster trap drums can be best heard at 1:59. Unknown if <PERSON> is on this version.", "length": "140.3", "fileDate": 17253216, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ea206f92a919920d0214d4f55724abd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ea206f92a919920d0214d4f55724abd7\", \"key\": \"LIFESTYLE\", \"title\": \"LIFESTYLE [V6]\", \"artists\": \"(prod. <PERSON>, FnZ & London on da Track)\", \"description\": \"Heard in the AI extracted vocal bleed of the April 29th, 2024 copy of \\\"Lifestyle\\\". Most likely just a different version of the song with faster trap drums that were in earlier versions and has <PERSON> doing vocals on the \\\"Husband\\\" part. Link provided is an inverted version, the part with faster trap drums can be best heard at 1:59. Unknown if <PERSON> is on this version.\", \"date\": 17253216, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"cb29eb307e6aeb07cd009d1d413aa844\", \"url\": \"https://api.pillowcase.su/api/download/cb29eb307e6aeb07cd009d1d413aa844\", \"size\": \"2.7 MB\", \"duration\": 140.3}", "aliases": [], "size": "2.7 MB"}, {"id": "lifestyle-44", "name": "🗑️ LIFESTYLE [V7]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "FnZ", "London on da Track"], "notes": "OG Filename: 042924_LIFESTYLE_150BPM_CMJAOR_\nHas no Ty and a DOGSHIT \"Husband\" section of the song with new vocals from <PERSON>, whose vocals are unmixed on the hook.", "length": "296", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c4970604f885144f0e6e0b3343d7765d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4970604f885144f0e6e0b3343d7765d\", \"key\": \"LIFESTYLE\", \"title\": \"\\ud83d\\uddd1\\ufe0f LIFESTYLE [V7]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, FnZ & London on da Track)\", \"description\": \"OG Filename: 042924_LIFESTYLE_150BPM_CMJAOR_\\nHas no Ty and a DOGSHIT \\\"Husband\\\" section of the song with new vocals from <PERSON>, whose vocals are unmixed on the hook.\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"41b5a2a75cf2728caa7009934dc57f8a\", \"url\": \"https://api.pillowcase.su/api/download/41b5a2a75cf2728caa7009934dc57f8a\", \"size\": \"5.19 MB\", \"duration\": 296}", "aliases": [], "size": "5.19 MB"}, {"id": "maybe", "name": "✨ MAYBE [V1]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "OG Filename: MAYBE Wes x Tay Edit 5_1_24\nVersion with production from Wes and taydex. Has a completely different beat from later versions of the song.", "length": "95.35", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/f80d92157afb4fc3867fb433f1da9552", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f80d92157afb4fc3867fb433f1da9552\", \"key\": \"MAYBE\", \"title\": \"\\u2728 MAYBE [V1]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"FOREVER\", \"MAYBE WE CAN LAST FOREVER\"], \"description\": \"OG Filename: <PERSON>YBE Wes x Tay Edit 5_1_24\\nVersion with production from <PERSON> and taydex. Has a completely different beat from later versions of the song.\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"afede222fba961c2fdcae77e4be00aed\", \"url\": \"https://api.pillowcase.su/api/download/afede222fba961c2fdcae77e4be00aed\", \"size\": \"1.98 MB\", \"duration\": 95.35}", "aliases": ["FOREVER", "MAYBE WE CAN LAST FOREVER"], "size": "1.98 MB"}, {"id": "melrose", "name": "MELROSE [V2]", "artists": [], "producers": ["TheLabCook"], "notes": "OG Filename: me<PERSON><PERSON> [ye freestyle edit 1]\nVersion made before <PERSON><PERSON> got on the song. These vocals would later be chopped for the version with <PERSON><PERSON>.", "length": "503.07", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/998ee3ffacd9e2e8969ab02915d68ef7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/998ee3ffacd9e2e8969ab02915d68ef7\", \"key\": \"MELROSE\", \"title\": \"MELROSE [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: mel<PERSON> [ye freestyle edit 1]\\nVersion made before <PERSON><PERSON> got on the song. These vocals would later be chopped for the version with <PERSON><PERSON>.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6d54f7dc864da16fa611445801ffbb4\", \"url\": \"https://api.pillowcase.su/api/download/d6d54f7dc864da16fa611445801ffbb4\", \"size\": \"8.51 MB\", \"duration\": 503.07}", "aliases": [], "size": "8.51 MB"}, {"id": "melrose-47", "name": "✨ MELROSE [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["TheLabCook", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON><PERSON>_\nEarlier 5 minute version that is paced faster compared to later versions. Has 4 minutes of mumble-ish Ye vocals, finished <PERSON> vocals and 1:30 of <PERSON><PERSON> vocals. Two snippets leaked July 10th, 2024, with the song fully leaking that same day due to a Carti comm groupbuy. Mixed by <PERSON>.", "length": "329.4", "fileDate": 17205696, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1fc5b5f4e7993fca56093b9eba620d33", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1fc5b5f4e7993fca56093b9eba620d33\", \"key\": \"MELROSE\", \"title\": \"\\u2728 MELROSE [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. TheLabCook & Ojivolta)\", \"description\": \"OG Filename: melrose_\\nEarlier 5 minute version that is paced faster compared to later versions. Has 4 minutes of mumble-ish Ye vocals, finished <PERSON> vocals and 1:30 of <PERSON><PERSON> vocals. Two snippets leaked July 10th, 2024, with the song fully leaking that same day due to a Carti comm groupbuy. Mixed by <PERSON>.\", \"date\": 17205696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b62f56e153abcc4613d3a133c21d3298\", \"url\": \"https://api.pillowcase.su/api/download/b62f56e153abcc4613d3a133c21d3298\", \"size\": \"5.73 MB\", \"duration\": 329.4}", "aliases": [], "size": "5.73 MB"}, {"id": "melrose-48", "name": "MELROSE [V4]", "artists": ["Quavo", "<PERSON><PERSON><PERSON>"], "producers": ["TheLabCook", "Ojivolta"], "notes": "OG Filename: MELROSE - quavo\nVersion  with a Quavo feature. Uses the \"Codeine\" sample, as well as the full <PERSON>, <PERSON> vocals from the February 20th version of the song. Original snippet leaked Dec 29th, 2024.", "length": "416.13", "fileDate": 17375040, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/a4c0f21db7f98c2767376a125488f737", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a4c0f21db7f98c2767376a125488f737\", \"key\": \"MELROSE\", \"title\": \"MELROSE [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>) (prod. TheLabCook & Ojivolta)\", \"description\": \"OG Filename: MELROSE - quavo\\nVersion  with a Quavo feature. Uses the \\\"Codeine\\\" sample, as well as the full <PERSON>, <PERSON> vocals from the February 20th version of the song. Original snippet leaked Dec 29th, 2024.\", \"date\": 17375040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25cd5bfc496115652c6c93f3895332db\", \"url\": \"https://api.pillowcase.su/api/download/25cd5bfc496115652c6c93f3895332db\", \"size\": \"7.12 MB\", \"duration\": 416.13}", "aliases": [], "size": "7.12 MB"}, {"id": "melrose-49", "name": "MELROSE [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["TheLabCook", "Ojivolta"], "notes": "OG Filename: MELROSE 3.8.24\nSong played during the private LA VULTURES 2 listening party. Features <PERSON><PERSON> vocals and new production. Shares same sample as \"Codeine\", which was added later in the track as it developed, but confirmed by <PERSON><PERSON> to be a separate song. Leaked after a sucessful Carti comm groupbuy. Mixed by <PERSON>.", "length": "200.66", "fileDate": 17205696, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/319354fee028dec5c37ff29f1ff8a429", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/319354fee028dec5c37ff29f1ff8a429\", \"key\": \"MELROSE\", \"title\": \"MELROSE [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. TheLabCook & Ojivolta)\", \"description\": \"OG Filename: MELROSE 3.8.24\\nSong played during the private LA VULTURES 2 listening party. Features <PERSON><PERSON> vocals and new production. Shares same sample as \\\"Codeine\\\", which was added later in the track as it developed, but confirmed by <PERSON><PERSON> to be a separate song. Leaked after a sucessful Carti comm groupbuy. Mixed by <PERSON>.\", \"date\": 17205696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"374390597bab1ab470ba2dcf75f6b5d6\", \"url\": \"https://api.pillowcase.su/api/download/374390597bab1ab470ba2dcf75f6b5d6\", \"size\": \"3.67 MB\", \"duration\": 200.66}", "aliases": [], "size": "3.67 MB"}, {"id": "fighting-fires", "name": "FIGHTING FIRES [V18]", "artists": ["<PERSON>", "Israel Boyd"], "producers": ["88-<PERSON>"], "notes": "OG Filename: fighting fires - 88 drums - v1\n88-Keys drum idea.", "length": "128.44", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d9fa6aede644e1425cfd4b86cb91d07a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9fa6aede644e1425cfd4b86cb91d07a\", \"key\": \"FIGHTING FIRES\", \"title\": \"FIGHTING FIRES [V18]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"MY SOUL\"], \"description\": \"OG Filename: fighting fires - 88 drums - v1\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"26660e92824cbb618aeaad79bdfb0018\", \"url\": \"https://api.pillowcase.su/api/download/26660e92824cbb618aeaad79bdfb0018\", \"size\": \"2.51 MB\", \"duration\": 128.44}", "aliases": ["Faithful", "Fightin Fire", "MY SOUL"], "size": "2.51 MB"}, {"id": "fighting-fires-51", "name": "FIGHTING FIRES [V19]", "artists": ["<PERSON>", "Israel Boyd"], "producers": ["88-<PERSON>"], "notes": "OG Filename: fighting fires - 88 drums - v2\n88-Keys drum idea. These drums were enchos to be used on release.", "length": "128.44", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/6a8c523304ad2f03fda6305691987420", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6a8c523304ad2f03fda6305691987420\", \"key\": \"FIGHTING FIRES\", \"title\": \"FIGHTING FIRES [V19]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"MY SOUL\"], \"description\": \"OG Filename: fighting fires - 88 drums - v2\\n88-Keys drum idea. These drums were enchos to be used on release.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ae2664f8bf771eb03fb597011ef9b8da\", \"url\": \"https://api.pillowcase.su/api/download/ae2664f8bf771eb03fb597011ef9b8da\", \"size\": \"2.51 MB\", \"duration\": 128.44}", "aliases": ["Faithful", "Fightin Fire", "MY SOUL"], "size": "2.51 MB"}, {"id": "fighting-fires-52", "name": "FIGHTING FIRES [V20]", "artists": ["<PERSON>", "Israel Boyd"], "producers": ["88-<PERSON>"], "notes": "OG Filename: fighting fires - 88 drums - v3\n88-Keys drum idea.", "length": "125.65", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/8ac38726f23dd3e28cf8632be2feb779", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ac38726f23dd3e28cf8632be2feb779\", \"key\": \"FIGHTING FIRES\", \"title\": \"FIGHTING FIRES [V20]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"MY SOUL\"], \"description\": \"OG Filename: fighting fires - 88 drums - v3\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fc433d3ca9eaa5038343246bbed807c0\", \"url\": \"https://api.pillowcase.su/api/download/fc433d3ca9eaa5038343246bbed807c0\", \"size\": \"2.47 MB\", \"duration\": 125.65}", "aliases": ["Faithful", "Fightin Fire", "MY SOUL"], "size": "2.47 MB"}, {"id": "fighting-fires-53", "name": "FIGHTING FIRES [V21]", "artists": ["<PERSON>", "Israel Boyd"], "producers": ["88-<PERSON>"], "notes": "OG Filename: fighting fires - 88 drums - v4\n88-Keys drum idea.", "length": "128.44", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/4341bd8428846c006f5e36949fac3b00", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4341bd8428846c006f5e36949fac3b00\", \"key\": \"FIGHTING FIRES\", \"title\": \"FIGHTING FIRES [V21]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"MY SOUL\"], \"description\": \"OG Filename: fighting fires - 88 drums - v4\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a70d258d0e97de831292d05b1debced7\", \"url\": \"https://api.pillowcase.su/api/download/a70d258d0e97de831292d05b1debced7\", \"size\": \"2.51 MB\", \"duration\": 128.44}", "aliases": ["Faithful", "Fightin Fire", "MY SOUL"], "size": "2.51 MB"}, {"id": "fighting-fires-54", "name": "FIGHTING FIRES [V22]", "artists": ["<PERSON>", "Israel Boyd"], "producers": ["88-<PERSON>"], "notes": "OG Filename: fighting fires - 88 drums - v5\n88-Keys drum idea.", "length": "128.44", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d3f363b0fad1f11f0dd0bb40d60bb2f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d3f363b0fad1f11f0dd0bb40d60bb2f1\", \"key\": \"FIGHTING FIRES\", \"title\": \"FIGHTING FIRES [V22]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"MY SOUL\"], \"description\": \"OG Filename: fighting fires - 88 drums - v5\\n88-Keys drum idea.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9a2990fc58e16483ae1c3ca1e2b3ffba\", \"url\": \"https://api.pillowcase.su/api/download/9a2990fc58e16483ae1c3ca1e2b3ffba\", \"size\": \"2.51 MB\", \"duration\": 128.44}", "aliases": ["Faithful", "Fightin Fire", "MY SOUL"], "size": "2.51 MB"}, {"id": "my-soul", "name": "MY SOUL [V23] ", "artists": ["<PERSON>"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "Updated version of \"Fighting Fires\" played during the private LA listening party for VULTURES 2 on March 9th, 2024. <PERSON>ign sings <PERSON>'s verse & chorus and <PERSON> raps the Arrow reference verse from 2020.", "length": "118.13", "fileDate": 17099424, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/9c2b47767aa15febdc91ccd15a043118", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c2b47767aa15febdc91ccd15a043118\", \"key\": \"MY SOUL\", \"title\": \"MY SOUL [V23] \", \"artists\": \"(feat. <PERSON>) (prod. BoogzDaBeast & FnZ) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"Updated version of \\\"Fighting Fires\\\" played during the private LA listening party for VULTURES 2 on March 9th, 2024. <PERSON>ign sings <PERSON>'s verse & chorus and <PERSON> raps the Arrow reference verse from 2020.\", \"date\": 17099424, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1a609ae4c56d5bc1aca0df7ed5bc2d7a\", \"url\": \"https://api.pillowcase.su/api/download/1a609ae4c56d5bc1aca0df7ed5bc2d7a\", \"size\": \"2.61 MB\", \"duration\": 118.13}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "2.61 MB"}, {"id": "new-body", "name": "NEW BODY [V39]", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: new body sm7 ref\nVersion that was bounced sometime in 2024. Newer mix of a version from 2018 with an alternate Ye verse. Has vocals from 0:34 to 1:25. Original snippet leaked September 28th, 2024.", "length": "101.07", "fileDate": 17277408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/e44bce1d8d96f7a6a211a10cb149b329", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e44bce1d8d96f7a6a211a10cb149b329\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V39]\", \"artists\": \"(prod. RONNY J)\", \"description\": \"OG Filename: new body sm7 ref\\nVersion that was bounced sometime in 2024. Newer mix of a version from 2018 with an alternate Ye verse. Has vocals from 0:34 to 1:25. Original snippet leaked September 28th, 2024.\", \"date\": 17277408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c51b3f1d90e165201757ec7cbbe09705\", \"url\": \"https://api.pillowcase.su/api/download/c51b3f1d90e165201757ec7cbbe09705\", \"size\": \"2.08 MB\", \"duration\": 101.07}", "aliases": [], "size": "2.08 MB"}, {"id": "not-inclusive", "name": "NOT INCLUSIVE [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG FIlename: NOT INCLUSIVE 3.30.24 MOOSE REF 1\nYoung Moose reference track.", "length": "134.74", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/e39c6b02d91cb8446ca2ba98039bc0fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e39c6b02d91cb8446ca2ba98039bc0fa\", \"key\": \"NOT INCLUSIVE\", \"title\": \"NOT INCLUSIVE [V2]\", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"description\": \"OG FIlename: NOT INCLUSIVE 3.30.24 MOOSE REF 1\\nYoung Moose reference track.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"05ab3a4e5c77cc6fdb44747f08313e9a\", \"url\": \"https://api.pillowcase.su/api/download/05ab3a4e5c77cc6fdb44747f08313e9a\", \"size\": \"2.61 MB\", \"duration\": 134.74}", "aliases": [], "size": "2.61 MB"}, {"id": "pay-per-view", "name": "PAY PER VIEW [V3]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>", "???"], "notes": "<PERSON> played during the private LA listening party for VULTURES 2 on March 9th, 2024. Features Project Pat vocals, and no <PERSON> vocals. Confirmed by <PERSON><PERSON> to be called \"Pay Per View\", was nicknamed \"<PERSON><PERSON>\" before the official title was known.", "length": "157.18", "fileDate": 17099424, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/0c5e2decfaab9df70cfa37311a735806", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0c5e2decfaab9df70cfa37311a735806\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, DJ <PERSON> ???)\", \"description\": \"Song played during the private LA listening party for VULTURES 2 on March 9th, 2024. Features <PERSON> Pat vocals, and no Ye vocals. Confirmed by <PERSON><PERSON> to be called \\\"Pay Per View\\\", was nicknamed \\\"Poppin\\\" before the official title was known.\", \"date\": 17099424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6c624fb72862bece237bbfc070accbee\", \"url\": \"https://api.pillowcase.su/api/download/6c624fb72862bece237bbfc070accbee\", \"size\": \"3.24 MB\", \"duration\": 157.18}", "aliases": [], "size": "3.24 MB"}, {"id": "pay-per-view-59", "name": "PAY PER VIEW [V4]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>", "???"], "notes": "OG Filename: <PERSON><PERSON> _ 3.10.24 SHOW \nPlayed during the Phoenix listening experience on March 10th, 2024.", "length": "144.55", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/bbda3242a9675b7ce4cbf1f8fdcdc156", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbda3242a9675b7ce4cbf1f8fdcdc156\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> ???)\", \"description\": \"OG Filename: PAy Per View _ 3.10.24 SHOW \\nPlayed during the Phoenix listening experience on March 10th, 2024.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"258e3d750c5be5da105a3b70b6f3bb5b\", \"url\": \"https://api.pillowcase.su/api/download/258e3d750c5be5da105a3b70b6f3bb5b\", \"size\": \"2.77 MB\", \"duration\": 144.55}", "aliases": [], "size": "2.77 MB"}, {"id": "pay-per-view-60", "name": "PAY PER VIEW [V5]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>", "The Legendary Traxster"], "notes": "OG Filename: PP TY YE 2 Traxster\nTraxster-produced version. Has more Project Pat vocals and some alternate Ty vocals on the chorus.", "length": "175.32", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/fb24475346f8d34844d33ed8acb54267", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fb24475346f8d34844d33ed8acb54267\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & The Legendary Traxster)\", \"description\": \"OG Filename: PP TY YE 2 Traxster\\nTraxster-produced version. Has more <PERSON> Pat vocals and some alternate Ty vocals on the chorus.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"52ecd1a01e5607fb984290dd4c512457\", \"url\": \"https://api.pillowcase.su/api/download/52ecd1a01e5607fb984290dd4c512457\", \"size\": \"3.26 MB\", \"duration\": 175.32}", "aliases": [], "size": "3.26 MB"}, {"id": "pay-per-view-61", "name": "PAY PER VIEW [V7]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "taydex"], "notes": "Solo Ty version, with alternate production from <PERSON> and taydex. Dated sometime in April 2024. Sam<PERSON> \"<PERSON><PERSON> Bitch, <PERSON><PERSON>e\" by Three 6 Mafia. Snippet leaked December 31st, 2024.", "length": "14.63", "fileDate": 17356032, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/4e5d74a791e09f37227e0f017ae1cbf7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e5d74a791e09f37227e0f017ae1cbf7\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & taydex)\", \"description\": \"Solo Ty version, with alternate production from <PERSON> and taydex. Dated sometime in April 2024. <PERSON><PERSON> \\\"<PERSON><PERSON> Bit<PERSON>, <PERSON><PERSON>\\\" by Three 6 Mafia. Snippet leaked December 31st, 2024.\", \"date\": 17356032, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2e52dacee68c2b764a9a60d892708165\", \"url\": \"https://api.pillowcase.su/api/download/2e52dacee68c2b764a9a60d892708165\", \"size\": \"692 kB\", \"duration\": 14.63}", "aliases": [], "size": "692 kB"}, {"id": "pay-per-view-62", "name": "PAY PER VIEW [V8]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "taydex"], "notes": "Solo Ty version, with alternate production from <PERSON> and taydex. Dated sometime in May 2024. Snippet leaked Jan 11th 2024.", "length": "8.36", "fileDate": 17365536, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/2efb971280e81cd1d9408b5cfa174260", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2efb971280e81cd1d9408b5cfa174260\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & taydex)\", \"description\": \"Solo Ty version, with alternate production from <PERSON> and taydex. Dated sometime in May 2024. Snippet leaked Jan 11th 2024.\", \"date\": 17365536, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a6b1da49aed4747fad7f25b6e0fbac17\", \"url\": \"https://api.pillowcase.su/api/download/a6b1da49aed4747fad7f25b6e0fbac17\", \"size\": \"591 kB\", \"duration\": 8.36}", "aliases": [], "size": "591 kB"}, {"id": "pay-per-view-63", "name": "PAY PER VIEW [V9]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>", "<PERSON><PERSON>"], "notes": "Has production from <PERSON><PERSON>. Has alternate Ty vocals. Sam<PERSON> \"Poppin' My Collar\" by Three 6 Mafia. Snippets leaked November 19th & 20th, 2024.", "length": "13.63", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/9492273e8d8c048032db89705f4fc658", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9492273e8d8c048032db89705f4fc658\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V9]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, DJ <PERSON>)\", \"description\": \"Has production from <PERSON><PERSON>. Has alternate Ty vocals. Samples \\\"Poppin' My Collar\\\" by Three 6 Mafia. Snippets leaked November 19th & 20th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ab7f4185c85f6e72d79dfa607fa8681c\", \"url\": \"https://api.pillowcase.su/api/download/ab7f4185c85f6e72d79dfa607fa8681c\", \"size\": \"675 kB\", \"duration\": 13.63}", "aliases": [], "size": "675 kB"}, {"id": "pay-per-view-64", "name": "PAY PER VIEW [V9]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>", "<PERSON><PERSON>"], "notes": "Has production from <PERSON><PERSON>. Has alternate Ty vocals. Sam<PERSON> \"Poppin' My Collar\" by Three 6 Mafia. Snippets leaked November 19th & 20th, 2024.", "length": "3.16", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/7f18ac67a1926d4a522d7c987ea49085", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7f18ac67a1926d4a522d7c987ea49085\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V9]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, DJ <PERSON>)\", \"description\": \"Has production from <PERSON><PERSON>. Has alternate Ty vocals. Samples \\\"Poppin' My Collar\\\" by Three 6 Mafia. Snippets leaked November 19th & 20th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"09c069de57aae3e3de2004f5a061a691\", \"url\": \"https://api.pillowcase.su/api/download/09c069de57aae3e3de2004f5a061a691\", \"size\": \"508 kB\", \"duration\": 3.16}", "aliases": [], "size": "508 kB"}, {"id": "promotion", "name": "PROMOTION [V12]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Played during the private LA listening party for VULTURES 2 on March 9th, 2024, but cut off early every time. Song partially played by <PERSON><PERSON> on stream July 23rd, 2024.", "length": "138.61", "fileDate": 17216928, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/6e8a7c62370727d065313065b72ce940", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e8a7c62370727d065313065b72ce940\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V12]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Played during the private LA listening party for VULTURES 2 on March 9th, 2024, but cut off early every time. Song partially played by <PERSON><PERSON> on stream July 23rd, 2024.\", \"date\": 17216928, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4ed5123f8344be8a0431c50523cf9dfa\", \"url\": \"https://api.pillowcase.su/api/download/4ed5123f8344be8a0431c50523cf9dfa\", \"size\": \"2.94 MB\", \"duration\": 138.61}", "aliases": ["GORGEOUS"], "size": "2.94 MB"}, {"id": "promotion-66", "name": "PROMOTION [V12]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Played during the private LA listening party for VULTURES 2 on March 9th, 2024, but cut off early every time. Song partially played by <PERSON><PERSON> on stream July 23rd, 2024.", "length": "103.7", "fileDate": 17216928, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/0fbb173252741948ed548f74f1e61e7a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0fbb173252741948ed548f74f1e61e7a\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V12]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Played during the private LA listening party for VULTURES 2 on March 9th, 2024, but cut off early every time. Song partially played by <PERSON><PERSON> on stream July 23rd, 2024.\", \"date\": 17216928, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9fe7e3c04a4f31f649d1c0c73af051b0\", \"url\": \"https://api.pillowcase.su/api/download/9fe7e3c04a4f31f649d1c0c73af051b0\", \"size\": \"2.12 MB\", \"duration\": 103.7}", "aliases": ["GORGEOUS"], "size": "2.12 MB"}, {"id": "promotion-67", "name": "PROMOTION [V13]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "OG Filename: PROMOTION - F1_\nPlayed during the Phoenix listening experience for VULTURES 2 on March 10th, 2024. Future has more audible bleed, also has 1 more Ty adlib compared to release and different effects on <PERSON>'s second verse and no autotune on the outro.", "length": "158.98", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/92662db26205f998724675c87153f461", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/92662db26205f998724675c87153f461\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V13]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track, The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"OG Filename: PROMOTION - F1_\\nPlayed during the Phoenix listening experience for VULTURES 2 on March 10th, 2024. <PERSON> has more audible bleed, also has 1 more Ty adlib compared to release and different effects on <PERSON>'s second verse and no autotune on the outro.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6ff0f9ffa06c75c4cba7a43ab3988030\", \"url\": \"https://api.pillowcase.su/api/download/6ff0f9ffa06c75c4cba7a43ab3988030\", \"size\": \"3 MB\", \"duration\": 158.98}", "aliases": ["GORGEOUS"], "size": "3 MB"}, {"id": "promotion-68", "name": "PROMOTION [V14]", "artists": ["Future"], "producers": ["???", "AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Has production from an unknown producer (incorrectly thought to be Cashmere Cat). Is said to have been made in March 2024.", "length": "159.5", "fileDate": 17232480, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/287974ab006f69663f463d47c9b7136c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/287974ab006f69663f463d47c9b7136c\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V14]\", \"artists\": \"(feat. Future) (prod. ???, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Has production from an unknown producer (incorrectly thought to be Cashmere Cat). Is said to have been made in March 2024.\", \"date\": 17232480, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4b1dc3316b7428169aa776e886675d8e\", \"url\": \"https://api.pillowcase.su/api/download/4b1dc3316b7428169aa776e886675d8e\", \"size\": \"3.01 MB\", \"duration\": 159.5}", "aliases": ["GORGEOUS"], "size": "3.01 MB"}, {"id": "promotion-69", "name": "PROMOTION [V15]", "artists": ["Future"], "producers": ["???", "AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Has production from an unknown producer. Snippet leaked October 28th, 2024.", "length": "18.34", "fileDate": 17300736, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/285e8a5b325b625492744551d2aaa7ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/285e8a5b325b625492744551d2aaa7ce\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V15]\", \"artists\": \"(feat. Future) (prod. ???, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Has production from an unknown producer. Snippet leaked October 28th, 2024.\", \"date\": 17300736, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"14c2ea7bbf2f65fc6474f96ed6e6f130\", \"url\": \"https://api.pillowcase.su/api/download/14c2ea7bbf2f65fc6474f96ed6e6f130\", \"size\": \"751 kB\", \"duration\": 18.34}", "aliases": ["GORGEOUS"], "size": "751 kB"}, {"id": "promotion-70", "name": "PROMOTION [V16]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Has an added vocoder over <PERSON>'s vocals. Snippet leaked October 28th, 2024.", "length": "27.27", "fileDate": 17300736, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/a1b1f847e1c8e9ea7fb8b6af8d5c6e93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a1b1f847e1c8e9ea7fb8b6af8d5c6e93\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V16]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track, The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Has an added vocoder over <PERSON>'s vocals. Snippet leaked October 28th, 2024.\", \"date\": 17300736, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"521e29e9d16acef720fefbea74041fe4\", \"url\": \"https://api.pillowcase.su/api/download/521e29e9d16acef720fefbea74041fe4\", \"size\": \"894 kB\", \"duration\": 27.27}", "aliases": ["GORGEOUS"], "size": "894 kB"}, {"id": "promotion-71", "name": "PROMOTION [V17]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster", "<PERSON><PERSON>"], "notes": "Has production from Cruza. Snippet leaked November 3rd, 2024.", "length": "4.68", "fileDate": 17305920, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/4dc2b547ee51236448b6e2be2de471d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4dc2b547ee51236448b6e2be2de471d2\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V17]\", \"artists\": \"(feat. Future) (prod. <PERSON>, <PERSON><PERSON><PERSON>, London on da Track, The Legendary Traxster & Cruza)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Has production from Cruza. Snippet leaked November 3rd, 2024.\", \"date\": 17305920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a303a2bb25475c5f5e2d234cd7a53d3f\", \"url\": \"https://api.pillowcase.su/api/download/a303a2bb25475c5f5e2d234cd7a53d3f\", \"size\": \"532 kB\", \"duration\": 4.68}", "aliases": ["GORGEOUS"], "size": "532 kB"}, {"id": "promotion-72", "name": "PROMOTION [V18]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster", "<PERSON>", "taydex"], "notes": "Has added production from Wes <PERSON>man & taydex. Snippet leaked January 1st, 2025", "length": "17.45", "fileDate": 17356896, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/24e4e33a36e1001a7b028318141ae908", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/24e4e33a36e1001a7b028318141ae908\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V18]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track, <PERSON> Legendary Traxster, Wes <PERSON> & taydex)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Has added production from Wes <PERSON>man & taydex. Snippet leaked January 1st, 2025\", \"date\": 17356896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"93e433572c34f567c738cca6b9d8531b\", \"url\": \"https://api.pillowcase.su/api/download/93e433572c34f567c738cca6b9d8531b\", \"size\": \"736 kB\", \"duration\": 17.45}", "aliases": ["GORGEOUS"], "size": "736 kB"}, {"id": "rave", "name": "RAVE! [V2]", "artists": ["JELEEL!"], "producers": ["<PERSON>", "rayotde", "LEV!"], "notes": "JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. <PERSON> Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.", "length": "107.26", "fileDate": 17339616, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/410844025a52bf6f7805a508a4a95595", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/410844025a52bf6f7805a508a4a95595\", \"key\": \"RAVE!\", \"title\": \"RAVE! [V2]\", \"artists\": \"(feat. JELEE<PERSON>!) (prod. <PERSON>, <PERSON><PERSON><PERSON> & LEV!)\", \"description\": \"JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. No Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.\", \"date\": 17339616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"99cc84bb6a0b8a2385619fad521b61e2\", \"url\": \"https://api.pillowcase.su/api/download/99cc84bb6a0b8a2385619fad521b61e2\", \"size\": \"2.44 MB\", \"duration\": 107.26}", "aliases": [], "size": "2.44 MB"}, {"id": "rave-74", "name": "RAVE! [V2]", "artists": ["JELEEL!"], "producers": ["<PERSON>", "rayotde", "LEV!"], "notes": "JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. <PERSON> Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.", "length": "", "fileDate": 17339616, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://youtu.be/CH9exnOLgXI?t=144", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://youtu.be/CH9exnOLgXI?t=144\", \"key\": \"RAVE!\", \"title\": \"RAVE! [V2]\", \"artists\": \"(feat. JELEE<PERSON>!) (prod. <PERSON>, ray<PERSON><PERSON> & LEV!)\", \"description\": \"JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. <PERSON> Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.\", \"date\": 17339616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "rave-75", "name": "RAVE! [V2]", "artists": ["JELEEL!"], "producers": ["<PERSON>", "rayotde", "LEV!"], "notes": "JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. <PERSON> Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.", "length": "14.47", "fileDate": 17339616, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/81d9e08090e47101fe189943bca4af93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/81d9e08090e47101fe189943bca4af93\", \"key\": \"RAVE!\", \"title\": \"RAVE! [V2]\", \"artists\": \"(feat. JELEEL!) (prod. <PERSON>, <PERSON><PERSON><PERSON> & LEV!)\", \"description\": \"JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. No Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.\", \"date\": 17339616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5149a246579120879c25f5947917e92b\", \"url\": \"https://api.pillowcase.su/api/download/5149a246579120879c25f5947917e92b\", \"size\": \"689 kB\", \"duration\": 14.47}", "aliases": [], "size": "689 kB"}, {"id": "rave-76", "name": "RAVE! [V2]", "artists": ["JELEEL!"], "producers": ["<PERSON>", "rayotde", "LEV!"], "notes": "JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. <PERSON> Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.", "length": "140.56", "fileDate": 17339616, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/0d3c081d0a634e746c8b61873bbfa23c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d3c081d0a634e746c8b61873bbfa23c\", \"key\": \"RAVE!\", \"title\": \"RAVE! [V2]\", \"artists\": \"(feat. J<PERSON>EE<PERSON>!) (prod. <PERSON>, <PERSON><PERSON><PERSON> & LEV!)\", \"description\": \"JELEEL! played an unreleased song at his Rolling Loud set that was apparently made for VULTURES. No Ty or Ye vocals on it so far. What was played at RL is not the full song, just part of it looped. Low quality snippet from the full song leaked October 23rd, 2024. Longer snippet was played by JELEEL! on Instgram live December 12th, 2024.\", \"date\": 17339616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"03666e4a95bf85911e0f240c960a2ecb\", \"url\": \"https://api.pillowcase.su/api/download/03666e4a95bf85911e0f240c960a2ecb\", \"size\": \"2.71 MB\", \"duration\": 140.56}", "aliases": [], "size": "2.71 MB"}, {"id": "river", "name": "RIVER [V21]", "artists": ["<PERSON> Thug", "The Hooligans"], "producers": ["AyoAA", "Digital Nas", "London on da Track"], "notes": "According to <PERSON><PERSON>, The Hooligans recorded for \"<PERSON>\" at the end of February. Snippet of The Hooligan's acapella stem leaked January 9th, 2025. Full acapella stem leaked Jan 11th 2024.", "length": "43.64", "fileDate": 17365536, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/4a5589761c6213b516fed2e7649986dc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a5589761c6213b516fed2e7649986dc\", \"key\": \"RIVER\", \"title\": \"RIVER [V21]\", \"artists\": \"(feat. <PERSON>hu<PERSON> & The Hooligans) (prod. <PERSON><PERSON><PERSON>, Digital Nas & London on da Track)\", \"description\": \"According to <PERSON><PERSON>, The Hooligans recorded for \\\"River\\\" at the end of February. Snippet of The Hooligan's acapella stem leaked January 9th, 2025. Full acapella stem leaked Jan 11th 2024.\", \"date\": 17365536, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"884a3428f4e04c20336d3a0fd5cc0620\", \"url\": \"https://api.pillowcase.su/api/download/884a3428f4e04c20336d3a0fd5cc0620\", \"size\": \"1.16 MB\", \"duration\": 43.64}", "aliases": [], "size": "1.16 MB"}, {"id": "river-78", "name": "RIVER [V22]", "artists": ["<PERSON> Thug"], "producers": ["AyoAA", "Digital Nas", "London on da Track"], "notes": "Played during the private LA listening party for VULTURES 2 on March 9th, 2024 with new production on <PERSON><PERSON><PERSON>'s part.", "length": "199.11", "fileDate": 17099424, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c28be3e47d04690e97c0be4ad90fcd39", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c28be3e47d04690e97c0be4ad90fcd39\", \"key\": \"RIVER\", \"title\": \"RIVER [V22]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, Digital Nas & London on da Track)\", \"description\": \"Played during the private LA listening party for VULTURES 2 on March 9th, 2024 with new production on <PERSON>hu<PERSON>'s part.\", \"date\": 17099424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ca5faada89ed1fa4880637773b79d4ef\", \"url\": \"https://api.pillowcase.su/api/download/ca5faada89ed1fa4880637773b79d4ef\", \"size\": \"3.91 MB\", \"duration\": 199.11}", "aliases": [], "size": "3.91 MB"}, {"id": "river-79", "name": "🗑️ RIVER [V23]", "artists": ["<PERSON> Thug"], "producers": ["???"], "notes": "Has production from an unknown source, incorrectly thought to be Cashmere Cat. Said to have been made in March 2024. Samples \"I Luv U\" by <PERSON><PERSON><PERSON>. <PERSON><PERSON> Ty vocals.", "length": "139.76", "fileDate": 17232480, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/0b8e52b0027ba528429de5c5f7e88cd5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b8e52b0027ba528429de5c5f7e88cd5\", \"key\": \"RIVER\", \"title\": \"\\ud83d\\uddd1\\ufe0f RIVER [V23]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. ???)\", \"description\": \"Has production from an unknown source, incorrectly thought to be Cashmere Cat. Said to have been made in March 2024. <PERSON><PERSON> \\\"I Luv U\\\" by <PERSON><PERSON><PERSON>. <PERSON> Ty vocals.\", \"date\": 17232480, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"881f1d46ef4e999ceff5cd5269087894\", \"url\": \"https://api.pillowcase.su/api/download/881f1d46ef4e999ceff5cd5269087894\", \"size\": \"2.69 MB\", \"duration\": 139.76}", "aliases": [], "size": "2.69 MB"}, {"id": "river-80", "name": "RIVER [V25]", "artists": ["<PERSON> Thug"], "producers": ["AyoAA", "Digital Nas", "London on da Track", "SHDØW"], "notes": "Has production from SHDØW that was \"fully made in Poland\". Previewed May 29th, 2024.", "length": "", "fileDate": 17169408, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/6011265af829b2d51144e034e8b8a447", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6011265af829b2d51144e034e8b8a447\", \"key\": \"RIVER\", \"title\": \"RIVER [V25]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod<PERSON> <PERSON><PERSON>, Digital Nas, London on da Track & SHD\\u00d8W)\", \"description\": \"Has production from SHD\\u00d8W that was \\\"fully made in Poland\\\". Previewed May 29th, 2024.\", \"date\": 17169408, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "slidin", "name": "SLIDIN [V27]", "artists": [], "producers": ["<PERSON> again.."], "notes": "OG Filename: <PERSON><PERSON><PERSON> MIX TEST.17_17\nPlayed at listening events (Phoenix) and basically identical to earlier versions. Unknown if there are any more mix tests after this. File played at the Pheonix LP leaked October 21st, 2024.", "length": "315.56", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5d2bf4365dc229ab4d8012bfd709cca5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d2bf4365dc229ab4d8012bfd709cca5\", \"key\": \"SLIDIN\", \"title\": \"SLIDIN [V27]\", \"artists\": \"(prod. <PERSON> again..)\", \"aliases\": [\"Slide In\", \"SLIDE\"], \"description\": \"OG Filename: Slidin MIX TEST.17_17\\nPlayed at listening events (Phoenix) and basically identical to earlier versions. Unknown if there are any more mix tests after this. File played at the Pheonix LP leaked October 21st, 2024.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b02d46ec966f811b47f370c7e75f1170\", \"url\": \"https://api.pillowcase.su/api/download/b02d46ec966f811b47f370c7e75f1170\", \"size\": \"5.51 MB\", \"duration\": 315.56}", "aliases": ["Slide In", "SLIDE"], "size": "5.51 MB"}, {"id": "slide", "name": "SLIDE [V28]", "artists": [], "producers": ["<PERSON> again.."], "notes": "Has a different mix and added strings, as well as the outro from older versions. Snippet leaked October 31st, 2024.", "length": "30.9", "fileDate": 17303328, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/288c917c4c7e388571472a4d444bcbae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/288c917c4c7e388571472a4d444bcbae\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V28]\", \"artists\": \"(prod. <PERSON> again..)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"Has a different mix and added strings, as well as the outro from older versions. Snippet leaked October 31st, 2024.\", \"date\": 17303328, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d38e6ccd463dcd88594b22aafebe4ad8\", \"url\": \"https://api.pillowcase.su/api/download/d38e6ccd463dcd88594b22aafebe4ad8\", \"size\": \"952 kB\", \"duration\": 30.9}", "aliases": ["Slide In", "SLIDIN"], "size": "952 kB"}, {"id": "slide-83", "name": "SLIDE [V29]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Has new production done by <PERSON> & taydex. Original snippet leaked Nov 2nd, second snip leaked Dec 31st, 2024.", "length": "36.44", "fileDate": 17356032, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/b8f978738f7adf7a1fba558a8dab65f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b8f978738f7adf7a1fba558a8dab65f4\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V29]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"Has new production done by Wes <PERSON>man & taydex. Original snippet leaked Nov 2nd, second snip leaked Dec 31st, 2024.\", \"date\": 17356032, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f72b105343173c1398f2b38ccb94b264\", \"url\": \"https://api.pillowcase.su/api/download/f72b105343173c1398f2b38ccb94b264\", \"size\": \"752 kB\", \"duration\": 36.44}", "aliases": ["Slide In", "SLIDIN"], "size": "752 kB"}, {"id": "slide-84", "name": "SLIDE [V29]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Has new production done by <PERSON> & taydex. Original snippet leaked Nov 2nd, second snip leaked Dec 31st, 2024.", "length": "19.03", "fileDate": 17356032, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/674336555cd07b769559f0cc4c7398e8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/674336555cd07b769559f0cc4c7398e8\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V29]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"Has new production done by Wes Singerman & taydex. Original snippet leaked Nov 2nd, second snip leaked Dec 31st, 2024.\", \"date\": 17356032, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cf5d6d3930cebf710aa958a1ebff9857\", \"url\": \"https://api.pillowcase.su/api/download/cf5d6d3930cebf710aa958a1ebff9857\", \"size\": \"762 kB\", \"duration\": 19.03}", "aliases": ["Slide In", "SLIDIN"], "size": "762 kB"}, {"id": "slide-85", "name": "SLIDE [V30]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Another Cruza produced version. Snippet leaked November 5th, 2024.", "length": "9.77", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/9a09ea7f4324adaadf4f5c7b578a5b8c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9a09ea7f4324adaadf4f5c7b578a5b8c\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V30]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"Another Cruza produced version. Snippet leaked November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1ce37b9c4e48c15ebe08d7570a7795ad\", \"url\": \"https://api.pillowcase.su/api/download/1ce37b9c4e48c15ebe08d7570a7795ad\", \"size\": \"614 kB\", \"duration\": 9.77}", "aliases": ["Slide In", "SLIDIN"], "size": "614 kB"}, {"id": "slide-86", "name": "🗑️ SLIDE [V31]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: Slide P2 108 (CRUZAFIED V1)\nHas new production done by <PERSON><PERSON>. Drums later got used on \"Time Moving Slow\". The fact that it is clean means this was most likely made in May when the album was censored.", "length": "195.56", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/217d684244d0707712270bee9fb45d62", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/217d684244d0707712270bee9fb45d62\", \"key\": \"SLIDE\", \"title\": \"\\ud83d\\uddd1\\ufe0f SLIDE [V31]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"OG Filename: Slide P2 108 (CRUZAFIED V1)\\nHas new production done by <PERSON><PERSON>. Drums later got used on \\\"Time Moving Slow\\\". The fact that it is clean means this was most likely made in May when the album was censored.\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5d176daee38fc41b96a77b4b8d4ad9ed\", \"url\": \"https://api.pillowcase.su/api/download/5d176daee38fc41b96a77b4b8d4ad9ed\", \"size\": \"3.59 MB\", \"duration\": 195.56}", "aliases": ["Slide In", "SLIDIN"], "size": "3.59 MB"}, {"id": "take-off-your-dress", "name": "TAKE OFF YOUR DRESS [V12]", "artists": [], "producers": ["<PERSON>", "SHDØW"], "notes": "Played during the private LA listening party for VULTURES 2.", "length": "164.21", "fileDate": 17099424, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ed3ee610052ae31ecf5dcb52713fe582", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ed3ee610052ae31ecf5dcb52713fe582\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"TAKE OFF YOUR DRESS [V12]\", \"artists\": \"(prod. <PERSON>way & SHD\\u00d8W)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"Played during the private LA listening party for VULTURES 2.\", \"date\": 17099424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2dc454c6ff60c7a336908ec74b3d20a5\", \"url\": \"https://api.pillowcase.su/api/download/2dc454c6ff60c7a336908ec74b3d20a5\", \"size\": \"3.35 MB\", \"duration\": 164.21}", "aliases": ["PAPA WANNA SEE"], "size": "3.35 MB"}, {"id": "take-off-your-dress-88", "name": "✨ TAKE OFF YOUR DRESS [V13]", "artists": [], "producers": ["<PERSON>", "SHDØW"], "notes": "OG Filename: TOYD MIX TEST_105.105\nHas <PERSON> replacing the sample, and fully finished <PERSON> vocals.", "length": "162.91", "fileDate": 17229888, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c559f7c7601aa109f83c69772c6c58eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c559f7c7601aa109f83c69772c6c58eb\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"\\u2728 TAKE OFF YOUR DRESS [V13]\", \"artists\": \"(prod. Scott <PERSON>way & SHD\\u00d8W)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"OG Filename: TOYD MIX TEST_105.105\\nHas Ty replacing the sample, and fully finished Ye vocals.\", \"date\": 17229888, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7b5f027d7074785ef7591b0bbee29ab8\", \"url\": \"https://api.pillowcase.su/api/download/7b5f027d7074785ef7591b0bbee29ab8\", \"size\": \"3.06 MB\", \"duration\": 162.91}", "aliases": ["PAPA WANNA SEE"], "size": "3.06 MB"}, {"id": "take-off-your-dress-89", "name": "TAKE OFF YOUR DRESS [V15]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: TAKE OFF YOUR DRESS P2 115 (CRUZAFIED V1)\nHas new production done by <PERSON><PERSON>. The fact that it is clean means this was most likely made in May when the album was censored.", "length": "150.26", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/166533f3fdd9e2329d9f55c74fecd40a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/166533f3fdd9e2329d9f55c74fecd40a\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"TAKE OFF YOUR DRESS [V15]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"OG Filename: TAKE OFF YOUR DRESS P2 115 (CRUZAFIED V1)\\nHas new production done by <PERSON><PERSON>. The fact that it is clean means this was most likely made in May when the album was censored.\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5c7c27fbe1f9c94cacf8611653be8842\", \"url\": \"https://api.pillowcase.su/api/download/5c7c27fbe1f9c94cacf8611653be8842\", \"size\": \"2.86 MB\", \"duration\": 150.26}", "aliases": ["PAPA WANNA SEE"], "size": "2.86 MB"}, {"id": "take-off-your-dress-90", "name": "TAKE OFF YOUR DRESS [V16]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Another version produced by Cruza. Snippet leaked November 30th, 2024.", "length": "9.69", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/72e2da6da6c0b1ee845a159b27cbbf48", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/72e2da6da6c0b1ee845a159b27cbbf48\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"TAKE OFF YOUR DRESS [V16]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"Another version produced by Cruza. Snippet leaked November 30th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"22c4467db0b6480d2af708d131475e84\", \"url\": \"https://api.pillowcase.su/api/download/22c4467db0b6480d2af708d131475e84\", \"size\": \"612 kB\", \"duration\": 9.69}", "aliases": ["PAPA WANNA SEE"], "size": "612 kB"}, {"id": "thirsty", "name": "THIRSTY [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: THIRSTY x MOOSE ref 1\nYoung Moose reference track. Has slightly different production to other versions. Leaked after a groupbuy.", "length": "150.43", "fileDate": 17319744, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ec20b21b2263542cb7ab153cef005129", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ec20b21b2263542cb7ab153cef005129\", \"key\": \"THIRSTY\", \"title\": \"THIRSTY [V2]\", \"artists\": \"(ref. <PERSON>) (prod. 88-Keys)\", \"description\": \"OG Filename: THIRSTY x MOOSE ref 1\\nYoung Moose reference track. Has slightly different production to other versions. Leaked after a groupbuy.\", \"date\": 17319744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"411ff5590b7926084baa57cb3e7bd10b\", \"url\": \"https://api.pillowcase.su/api/download/411ff5590b7926084baa57cb3e7bd10b\", \"size\": \"2.86 MB\", \"duration\": 150.43}", "aliases": [], "size": "2.86 MB"}, {"id": "thirsty-92", "name": "THIRSTY [V3]", "artists": ["<PERSON><PERSON>", "Teezo Touchdown"], "producers": ["The Legendary Traxster", "88-<PERSON>", "White Armor"], "notes": "OG Filename: 041524 IDEA3 TRAXS LEAN YE 88 WHITEARMOR 80BPM CMAJOR OSC TEEZO TOUCHDOWN FREEYSTLE 2\nInitial freestyle, with finished vocals from <PERSON><PERSON> and finished/mumble vocals from <PERSON><PERSON><PERSON>down. Would be cut down for later versions. Has live production. Leaked after a groupbuy.", "length": "", "fileDate": 17345664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/92c1e7a318e86dc813c79aeb353d98f6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/92c1e7a318e86dc813c79aeb353d98f6\", \"key\": \"THIRSTY\", \"title\": \"THIRSTY [V3]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON><PERSON><PERSON> Touchdown) (prod. The Legendary Traxster, 88-Keys & White Armor)\", \"description\": \"OG Filename: 041524 IDEA3 TRAXS LEAN YE 88 WHITEARMOR 80BPM CMAJOR OSC TEEZO TOUCHDOWN FREEYSTLE 2\\nInitial freestyle, with finished vocals from <PERSON><PERSON> and finished/mumble vocals from <PERSON><PERSON><PERSON>down. Would be cut down for later versions. Has live production. Leaked after a groupbuy.\", \"date\": 17345664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "thirsty-93", "name": "✨ THIRSTY [V4]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: 042924 THIRSTY 110BPM Dbmajor\nSamples \"Third Coast\" by <PERSON>ezo Touchdown. <PERSON> fully finished <PERSON> vocals with him going back and forth with <PERSON><PERSON><PERSON>. Original snippets leaked October 31st, 2024 & November 8th, 2024, before leaking after a groupbuy.", "length": "151.04", "fileDate": 17319744, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d0022aa561b17a2e50813f1996e2883b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d0022aa561b17a2e50813f1996e2883b\", \"key\": \"THIRSTY\", \"title\": \"\\u2728 THIRSTY [V4]\", \"artists\": \"(prod. 88-Keys)\", \"description\": \"OG Filename: 042924 THIRSTY 110BPM Dbmajor\\nSamples \\\"Third Coast\\\" by Teezo Touchdown. Has fully finished <PERSON> vocals with him going back and forth with <PERSON><PERSON><PERSON>. Original snippets leaked October 31st, 2024 & November 8th, 2024, before leaking after a groupbuy.\", \"date\": 17319744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"858141ca9a060094bf7df3ce8a117439\", \"url\": \"https://api.pillowcase.su/api/download/858141ca9a060094bf7df3ce8a117439\", \"size\": \"2.87 MB\", \"duration\": 151.04}", "aliases": [], "size": "2.87 MB"}, {"id": "thirsty-94", "name": "THIRSTY [V6]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: THIRSTY 120 CRUZAFIED V2\nHas alternate production done by <PERSON><PERSON>. Also features extra vocals from one of <PERSON><PERSON>'s members. Leaked after a groupbuy.", "length": "224", "fileDate": 17319744, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/4b8fbc0d9f58be35df8d2904676adc2a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4b8fbc0d9f58be35df8d2904676adc2a\", \"key\": \"THIRSTY\", \"title\": \"THIRSTY [V6]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: THIRSTY 120 CRUZAFIED V2\\nHas alternate production done by <PERSON><PERSON>. Also features extra vocals from one of <PERSON><PERSON>'s members. Leaked after a groupbuy.\", \"date\": 17319744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bd14f50b3ae3267e4f7dfdaa486a1329\", \"url\": \"https://api.pillowcase.su/api/download/bd14f50b3ae3267e4f7dfdaa486a1329\", \"size\": \"4.04 MB\", \"duration\": 224}", "aliases": [], "size": "4.04 MB"}, {"id": "time-moving-slow", "name": "TIME MOVING SLOW [V23]", "artists": [], "producers": ["AyoAA", "<PERSON><PERSON>", "SHDØW"], "notes": "Played by <PERSON><PERSON><PERSON><PERSON> at an event on March 23rd, 2024. Features a different kick pattern and quiet high pitched vocals. Possibly not a recent version, but definitely updated from previous known versions. Longer snippet previewed by <PERSON><PERSON><PERSON><PERSON> again on an Instagram live on May 29th, 2024.", "length": "54.07", "fileDate": 17116704, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1dc253dda6158091416ec3b605b20266", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1dc253dda6158091416ec3b605b20266\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V23]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>ya Man & SHD\\u00d8W)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Played by SHD\\u00d8W at an event on March 23rd, 2024. Features a different kick pattern and quiet high pitched vocals. Possibly not a recent version, but definitely updated from previous known versions. Longer snippet previewed by SHD\\u00d8W again on an Instagram live on May 29th, 2024.\", \"date\": 17116704, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b93ab7403c7d0bfe70456d1bea8ba14c\", \"url\": \"https://api.pillowcase.su/api/download/b93ab7403c7d0bfe70456d1bea8ba14c\", \"size\": \"1.59 MB\", \"duration\": 54.07}", "aliases": ["TIME MOVES SLOW"], "size": "1.59 MB"}, {"id": "time-moving-slow-96", "name": "TIME MOVING SLOW [V23]", "artists": [], "producers": ["AyoAA", "<PERSON><PERSON>", "SHDØW"], "notes": "Played by <PERSON><PERSON><PERSON><PERSON> at an event on March 23rd, 2024. Features a different kick pattern and quiet high pitched vocals. Possibly not a recent version, but definitely updated from previous known versions. Longer snippet previewed by <PERSON><PERSON><PERSON><PERSON> again on an Instagram live on May 29th, 2024.", "length": "18.18", "fileDate": 17116704, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/8b2fbea83876aec82e9b260ad9f42f47", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b2fbea83876aec82e9b260ad9f42f47\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V23]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON> Man & SHD\\u00d8W)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Played by SHD\\u00d8W at an event on March 23rd, 2024. Features a different kick pattern and quiet high pitched vocals. Possibly not a recent version, but definitely updated from previous known versions. Longer snippet previewed by SHD\\u00d8W again on an Instagram live on May 29th, 2024.\", \"date\": 17116704, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0a0746eb9278744113670571bef3c268\", \"url\": \"https://api.pillowcase.su/api/download/0a0746eb9278744113670571bef3c268\", \"size\": \"1.01 MB\", \"duration\": 18.18}", "aliases": ["TIME MOVES SLOW"], "size": "1.01 MB"}, {"id": "ye-about-mine", "name": "YE ABOUT MINE [V2]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless"], "notes": "Version with an open verse. Is a stem bounce.", "length": "150", "fileDate": 17305920, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c79b57b895daf3161fe52a3f8be2ca63", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c79b57b895daf3161fe52a3f8be2ca63\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V2]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON> & <PERSON><PERSON><PERSON> Never Broke Again) (prod. Bud<PERSON> Bless)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"Version with an open verse. Is a stem bounce.\", \"date\": 17305920, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"451a7eea57f05ba728b113d4f301be1c\", \"url\": \"https://api.pillowcase.su/api/download/451a7eea57f05ba728b113d4f301be1c\", \"size\": \"2.86 MB\", \"duration\": 150}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "2.86 MB"}, {"id": "ye-about-mine-98", "name": "YE ABOUT MINE [V3]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless"], "notes": "OG Filename: Ye About Mine - 230308\nHas an offbeat Ye verse. The \"23\" in the filename was most likely a mistake. Original snippet leaked August 21st, 2024.", "length": "150.01", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/c753920f3f7729da4fdc7919a172721f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c753920f3f7729da4fdc7919a172721f\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V3]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON> & <PERSON>B<PERSON> Never Broke Again) (prod. Buddah Bless)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"OG Filename: Ye About Mine - 230308\\nHas an offbeat Ye verse. The \\\"23\\\" in the filename was most likely a mistake. Original snippet leaked August 21st, 2024.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0d0ea553c348639e30a90915b48e9017\", \"url\": \"https://api.pillowcase.su/api/download/0d0ea553c348639e30a90915b48e9017\", \"size\": \"2.86 MB\", \"duration\": 150.01}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "2.86 MB"}, {"id": "ye-about-mine-99", "name": "YE ABOUT MINE [V4]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless"], "notes": "OG Filename: YE ABOUT MINE - ye ref 1\nHas <PERSON> harmonising on the beat instead of rapping.", "length": "150.01", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/8b3d7971135fff19d985512d784b6f11", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b3d7971135fff19d985512d784b6f11\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V4]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON> & <PERSON>B<PERSON> Never Broke Again) (prod. Buddah Bless)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"OG Filename: YE ABOUT MINE - ye ref 1\\nHas Ye harmonising on the beat instead of rapping.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"25a1a457fec2608a8c2d703d0f15e3df\", \"url\": \"https://api.pillowcase.su/api/download/25a1a457fec2608a8c2d703d0f15e3df\", \"size\": \"2.86 MB\", \"duration\": 150.01}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "2.86 MB"}, {"id": "ye-about-mine-100", "name": "YE ABOUT MINE [V5]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless"], "notes": "OG Filename: YE ABOUT MINE - ye ref 2\nBasically a better mix of the March 8th version and has <PERSON>'s verse on beat.", "length": "150.1", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/0e5d101c1d55a527bc854b19ad16476f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0e5d101c1d55a527bc854b19ad16476f\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V5]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON> & <PERSON><PERSON><PERSON> Never Broke Again) (prod. Bud<PERSON> Bless)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"OG Filename: YE ABOUT MINE - ye ref 2\\nBasically a better mix of the March 8th version and has <PERSON>'s verse on beat.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"96a1418dec60ce9b1f460b307985815f\", \"url\": \"https://api.pillowcase.su/api/download/96a1418dec60ce9b1f460b307985815f\", \"size\": \"2.86 MB\", \"duration\": 150.1}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "2.86 MB"}, {"id": "ye-about-mine-101", "name": "YE ABOUT MINE [V6]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless", "???"], "notes": "Song played during the private LA listening party for VULTURES 2 on March 9th, 2024. Originally an unreleased Takeoff x Rich The Kid x NBA Youngboy song titled \"Let Me Chill Out.\" <PERSON> The Kid later confirmed that NBA Youngboy is still featured. Title confirmed to be \"Ye About Mine\" by <PERSON><PERSON>. <PERSON><PERSON> also confirmed that an alternate name used for the song at one point was \"MOTION\".", "length": "67.11", "fileDate": 17099424, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/74ffc06003d6055a4b208a8e9bf7dc98", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/74ffc06003d6055a4b208a8e9bf7dc98\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V6]\", \"artists\": \"(feat. <PERSON> Kid, <PERSON><PERSON> & <PERSON>B<PERSON> Never Broke Again) (prod. Buddah Bless & ???)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"Song played during the private LA listening party for VULTURES 2 on March 9th, 2024. Originally an unreleased Takeoff x Rich The Kid x NBA Youngboy song titled \\\"Let Me Chill Out.\\\" <PERSON> The Kid later confirmed that NBA Youngboy is still featured. Title confirmed to be \\\"Ye About Mine\\\" by Luit. Luit also confirmed that an alternate name used for the song at one point was \\\"MOTION\\\".\", \"date\": 17099424, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c531f17a3057e72d5fe99f9756701848\", \"url\": \"https://api.pillowcase.su/api/download/c531f17a3057e72d5fe99f9756701848\", \"size\": \"1.79 MB\", \"duration\": 67.11}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "1.79 MB"}, {"id": "", "name": "???", "artists": ["???"], "producers": ["JPEGMAFIA"], "notes": "Song played during the private LA listening party for VULTURES 2 on March 9th, 2024. Features <PERSON>$ vocals and a prominent sample of \"Bonafide Love\" by <PERSON><PERSON><PERSON> and <PERSON>. Has no Ye vocals.", "length": "88.25", "fileDate": 17099424, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/2467fe0e063264a166b9243b56a3d5ed", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2467fe0e063264a166b9243b56a3d5ed\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(feat. ???) (prod. JPEGMAFIA)\", \"aliases\": [\"GONE\"], \"description\": \"Song played during the private LA listening party for VULTURES 2 on March 9th, 2024. Features <PERSON>$ vocals and a prominent sample of \\\"Bonafide Love\\\" by <PERSON><PERSON><PERSON> and <PERSON>. Has no Ye vocals.\", \"date\": 17099424, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b456fdd529235a336e495df1a0c75262\", \"url\": \"https://api.pillowcase.su/api/download/b456fdd529235a336e495df1a0c75262\", \"size\": \"2.13 MB\", \"duration\": 88.25}", "aliases": ["GONE"], "size": "2.13 MB"}, {"id": "-103", "name": "???", "artists": [], "producers": ["SHDØW"], "notes": "Unknown song. Played in an Instagram Live by SHDØW.", "length": "3.55", "fileDate": 17169408, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/9c104bd3ce7ceb3303c7f1a7ba01cbe8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c104bd3ce7ceb3303c7f1a7ba01cbe8\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. SHD\\u00d8W)\", \"description\": \"Unknown song. Played in an Instagram Live by SHD\\u00d8W.\", \"date\": 17169408, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3412429c54cb10b34a687e5ff84699ad\", \"url\": \"https://api.pillowcase.su/api/download/3412429c54cb10b34a687e5ff84699ad\", \"size\": \"777 kB\", \"duration\": 3.55}", "aliases": [], "size": "777 kB"}, {"id": "-104", "name": "???", "artists": [], "producers": [], "notes": "Snippet posted by French Montana on March 3rd, 2024. Confirmed by Pop to not feature French Montana. Song is thought by some to be \"Aperol Spritz\", however according to <PERSON><PERSON> it is likely not.", "length": "9.93", "fileDate": 17094240, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/8eac1f5c3b7d1ab81766b093f51d59b9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8eac1f5c3b7d1ab81766b093f51d59b9\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Snippet posted by French <PERSON> on March 3rd, 2024. Confirmed by Pop to not feature French Montana. Song is thought by some to be \\\"Aperol Spritz\\\", however according to <PERSON><PERSON> it is likely not.\", \"date\": 17094240, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7150af7c19cadcc7f63a950361d5b7e4\", \"url\": \"https://api.pillowcase.su/api/download/7150af7c19cadcc7f63a950361d5b7e4\", \"size\": \"879 kB\", \"duration\": 9.93}", "aliases": [], "size": "879 kB"}, {"id": "like-that", "name": "Future & Metro Boomin - Like That (Remix) [V1]", "artists": ["Kanye West"], "producers": ["Metro Boomin"], "notes": "The earliest known version of the \"Like That\" remix, with alternate <PERSON> vocals. Snippet leaked April 20th, 2024, with a longer one leaking May 20th, 2024.", "length": "47.99", "fileDate": 17161632, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ef5e69835b1477ab2ecc5d5575e93a97", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef5e69835b1477ab2ecc5d5575e93a97\", \"key\": \"Like That (Remix)\", \"title\": \"Future & Metro Boomin - Like That (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Metro Boomin)\", \"description\": \"The earliest known version of the \\\"Like That\\\" remix, with alternate Ye vocals. Snippet leaked April 20th, 2024, with a longer one leaking May 20th, 2024.\", \"date\": 17161632, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2637a3474130d75cc131ee7388fe671d\", \"url\": \"https://api.pillowcase.su/api/download/2637a3474130d75cc131ee7388fe671d\", \"size\": \"938 kB\", \"duration\": 47.99}", "aliases": [], "size": "938 kB"}, {"id": "like-that-106", "name": "Future & Metro Boomin - Like That (Remix) [V1]", "artists": ["Kanye West"], "producers": ["Metro Boomin"], "notes": "The earliest known version of the \"Like That\" remix, with alternate <PERSON> vocals. Snippet leaked April 20th, 2024, with a longer one leaking May 20th, 2024.", "length": "21.86", "fileDate": 17161632, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/50324ec9769b02aff9d0810282fd989b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/50324ec9769b02aff9d0810282fd989b\", \"key\": \"Like That (Remix)\", \"title\": \"Future & Metro Boomin - Like That (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Metro Boomin)\", \"description\": \"The earliest known version of the \\\"Like That\\\" remix, with alternate Ye vocals. Snippet leaked April 20th, 2024, with a longer one leaking May 20th, 2024.\", \"date\": 17161632, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fab32a420677d70c008673bc0ae9cf14\", \"url\": \"https://api.pillowcase.su/api/download/fab32a420677d70c008673bc0ae9cf14\", \"size\": \"521 kB\", \"duration\": 21.86}", "aliases": [], "size": "521 kB"}, {"id": "like-that-107", "name": "Future & Metro Boomin - Like That (Remix) [V2]", "artists": ["Kanye West"], "producers": ["Metro Boomin"], "notes": "Initial version of the \"Like That\" remix, over the original instrumental for the song, no <PERSON> vocals, and some extra/different <PERSON> vocals & adlibs that would later be cut. Previewed by Adam22 on April 20th, 2024. It's unknown if <PERSON><PERSON> is still on this version. Longer snippet with all of the <PERSON> verse leaked May 26th, 2024. Interpolates \"Celebration\".", "length": "122.78", "fileDate": 17166816, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/fd067c4aec59cfc67333508610f2ad0a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fd067c4aec59cfc67333508610f2ad0a\", \"key\": \"Like That (Remix)\", \"title\": \"Future & Metro Boomin - Like That (Remix) [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Metro Boomin)\", \"description\": \"Initial version of the \\\"Like That\\\" remix, over the original instrumental for the song, no Ty vocals, and some extra/different Ye vocals & adlibs that would later be cut. Previewed by Adam22 on April 20th, 2024. It's unknown if <PERSON><PERSON> is still on this version. Longer snippet with all of the Ye verse leaked May 26th, 2024. Interpolates \\\"Celebration\\\".\", \"date\": 17166816, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"39199aaafa831ad2895a7599167167a3\", \"url\": \"https://api.pillowcase.su/api/download/39199aaafa831ad2895a7599167167a3\", \"size\": \"2.14 MB\", \"duration\": 122.78}", "aliases": [], "size": "2.14 MB"}, {"id": "like-that-108", "name": "Future & Metro Boomin - LIKE THAT (Remix) [V4]", "artists": ["¥$", "The Hooligans"], "producers": ["Metro Boomin", "Kanye West", "The Legendary Traxster"], "notes": "Previewed on the <PERSON> \"The Download\" interview. Has the Eazy sample before The Hooligans start, apart from that it's the same as release. According to Insiders, the \"Like That Remix\" was intended to be a single for VULTURES 2, but plans got scrapped.", "length": "113.78", "fileDate": 17136576, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/2986c1f5e69b99b40f8331ea29291432", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2986c1f5e69b99b40f8331ea29291432\", \"key\": \"LIKE THAT (Remix)\", \"title\": \"Future & Metro Boomin - LIKE THAT (Remix) [V4]\", \"artists\": \"(feat. \\u00a5$ & The Hooligans) (prod. Metro Boomin, Kanye West & The Legendary Traxster)\", \"description\": \"Previewed on the Justin Laboy \\\"The Download\\\" interview. Has the Eazy sample before The Hooligans start, apart from that it's the same as release. According to Insiders, the \\\"Like That Remix\\\" was intended to be a single for VULTURES 2, but plans got scrapped.\", \"date\": 17136576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"dcf60974db78ac890c94fc59151e3040\", \"url\": \"https://api.pillowcase.su/api/download/dcf60974db78ac890c94fc59151e3040\", \"size\": \"2.54 MB\", \"duration\": 113.78}", "aliases": [], "size": "2.54 MB"}, {"id": "530", "name": "530 [V27]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX"], "notes": "Played at the 2nd China LP that features a Ye verse finished using AI, and additional <PERSON> $ign vocals. Features a line about <PERSON><PERSON> that was removed for release.", "length": "294.06", "fileDate": 17274816, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/adef0221577f71de43b9e06bb31d00c4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/adef0221577f71de43b9e06bb31d00c4\", \"key\": \"530\", \"title\": \"530 [V27]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & E.VAX)\", \"aliases\": [\"The Car's Missing\", \"Dear Summer\", \"5:30\", \"530 AM\"], \"description\": \"Played at the 2nd China LP that features a Ye verse finished using AI, and additional Ty Dolla $ign vocals. Features a line about <PERSON><PERSON> that was removed for release.\", \"date\": 17274816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"834fb16e2063f03f80e47699b2e0a4b1\", \"url\": \"https://api.pillowcase.su/api/download/834fb16e2063f03f80e47699b2e0a4b1\", \"size\": \"5.16 MB\", \"duration\": 294.06}", "aliases": ["The Car's Missing", "Dear Summer", "5:30", "530 AM"], "size": "5.16 MB"}, {"id": "530-110", "name": "530 [V28]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX"], "notes": "Version used in the song's music video. Seemingly unmixed.", "length": "258.09", "fileDate": 17362944, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/91ad1551e00c0b252484419e71a44d44", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91ad1551e00c0b252484419e71a44d44\", \"key\": \"530\", \"title\": \"530 [V28]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & E.VAX)\", \"aliases\": [\"The Car's Missing\", \"Dear Summer\", \"5:30\", \"530 AM\"], \"description\": \"Version used in the song's music video. Seemingly unmixed.\", \"date\": 17362944, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ddecea5c274a69670f73d04fdec12cc1\", \"url\": \"https://api.pillowcase.su/api/download/ddecea5c274a69670f73d04fdec12cc1\", \"size\": \"4.59 MB\", \"duration\": 258.09}", "aliases": ["The Car's Missing", "Dear Summer", "5:30", "530 AM"], "size": "4.59 MB"}, {"id": "after-lyfe", "name": "AFTER LYFE [V1]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "OG Filename: After Lyfe FINAL (wes x tay)\nTrack initially known through anonymous sources. Seen on two leaked VULTURES 2 tracklists. <PERSON> Ty with open verses.", "length": "168.56", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/8d9a7a9a3f217dd6f3ee7911c19b5248", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8d9a7a9a3f217dd6f3ee7911c19b5248\", \"key\": \"AFTER LYFE\", \"title\": \"AFTER LYFE [V1]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"description\": \"OG Filename: After Lyfe FINAL (wes x tay)\\nTrack initially known through anonymous sources. Seen on two leaked VULTURES 2 tracklists. Solo Ty with open verses.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6f8d3d9442fcb601d1cd7e2db7525f64\", \"url\": \"https://api.pillowcase.su/api/download/6f8d3d9442fcb601d1cd7e2db7525f64\", \"size\": \"3.16 MB\", \"duration\": 168.56}", "aliases": [], "size": "3.16 MB"}, {"id": "believer-112", "name": "BELIEVER [V13] ", "artists": ["???"], "producers": ["<PERSON><PERSON>"], "notes": "Cruza prod version of \"Believer\". Snippet leaked November 5th, 2024.", "length": "8.63", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/83c2c2809517ef48728b39299c3b486c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/83c2c2809517ef48728b39299c3b486c\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V13] \", \"artists\": \"(feat. ???) (prod. <PERSON><PERSON>)\", \"description\": \"Cruza prod version of \\\"Believer\\\". Snippet leaked November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"61d47f7a3b6d695de55407eba9db6377\", \"url\": \"https://api.pillowcase.su/api/download/61d47f7a3b6d695de55407eba9db6377\", \"size\": \"596 kB\", \"duration\": 8.63}", "aliases": [], "size": "596 kB"}, {"id": "believer-113", "name": "BELIEVER [V14]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Version of \"BELIEVER\" with added production from Wes <PERSON> & taydex. Snippet leaked January 3rd, 2025.", "length": "12.64", "fileDate": 17358624, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/b79319897e9b183d7c742992a0f3be11", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b79319897e9b183d7c742992a0f3be11\", \"key\": \"BELIEVER\", \"title\": \"BELIEVER [V14]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"description\": \"Version of \\\"BELIEVER\\\" with added production from Wes <PERSON>man & taydex. Snippet leaked January 3rd, 2025.\", \"date\": 17358624, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"86673f9d0e43433279022005fae0a0f1\", \"url\": \"https://api.pillowcase.su/api/download/86673f9d0e43433279022005fae0a0f1\", \"size\": \"660 kB\", \"duration\": 12.64}", "aliases": [], "size": "660 kB"}, {"id": "can-u-be", "name": "CAN U BE [V14]", "artists": ["<PERSON>"], "producers": ["Havoc"], "notes": "Updated version of \"Can U Be\" played at the China LP. Has different sequencing and mixing.", "length": "147.5", "fileDate": 17263584, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/5f7db3129ca52de3fe7f3218945b62ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f7db3129ca52de3fe7f3218945b62ce\", \"key\": \"CAN U BE\", \"title\": \"CAN U BE [V14]\", \"artists\": \"(feat. <PERSON>) (prod. Havoc)\", \"aliases\": [\"Pressure\", \"Never Let Me Go\"], \"description\": \"Updated version of \\\"Can U Be\\\" played at the China LP. Has different sequencing and mixing.\", \"date\": 17263584, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"34b231ccbb8830faa11ba831ccd9dfc9\", \"url\": \"https://api.pillowcase.su/api/download/34b231ccbb8830faa11ba831ccd9dfc9\", \"size\": \"1.64 MB\", \"duration\": 147.5}", "aliases": ["Pressure", "Never Let Me Go"], "size": "1.64 MB"}, {"id": "field-trip-115", "name": "FIELD TRIP [V25]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "Real Hubi", "SHDØW", "<PERSON><PERSON>", "<PERSON>", "Timbaland", "Ojivolta", "The Legendary Traxster"], "notes": "OG Filename: FIELD TRIP YE REF (5.7.24)\nVersion with the newly recorded Durk verse, different mixing, and a mumble Ye verse. Has no <PERSON><PERSON>.", "length": "192.91", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/81eca87e179a6164738e35c875a53e59", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/81eca87e179a6164738e35c875a53e59\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V25]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>\\u00d8<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Ojivolta & The Legendary Traxster)\", \"description\": \"OG Filename: FIELD TRIP YE REF (5.7.24)\\nVersion with the newly recorded Durk verse, different mixing, and a mumble Ye verse. Has no Kodak.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c6f62f416fd0a76f229c1c929cce81f4\", \"url\": \"https://api.pillowcase.su/api/download/c6f62f416fd0a76f229c1c929cce81f4\", \"size\": \"3.55 MB\", \"duration\": 192.91}", "aliases": [], "size": "3.55 MB"}, {"id": "field-trip-116", "name": "FIELD TRIP [V26]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Kodak Black"], "producers": ["EJ PARIS", "Wheezy", "<PERSON><PERSON>", "<PERSON><PERSON>", "The Legendary Traxster", "AyoAA", "IRKO"], "notes": "Played at the Korea LP. Is seemingly the IRKO mix, but a single Kodak line is uncensored. Higher quality rip with less crowd noise later leaked.", "length": "163.66", "fileDate": 17243712, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/63f7b107d3f3dc6ce2c81241da6a31a2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63f7b107d3f3dc6ce2c81241da6a31a2\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V26]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, The Legendary Traxster, AyoAA & IRKO)\", \"description\": \"Played at the Korea LP. Is seemingly the IRKO mix, but a single Kodak line is uncensored. Higher quality rip with less crowd noise later leaked.\", \"date\": 17243712, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5037f886980657ca69e2e8f33c6756cd\", \"url\": \"https://api.pillowcase.su/api/download/5037f886980657ca69e2e8f33c6756cd\", \"size\": \"3.08 MB\", \"duration\": 163.66}", "aliases": [], "size": "3.08 MB"}, {"id": "fortune-and-fame", "name": "FORTUNE AND FAME [V2]", "artists": [], "producers": [], "notes": "OG Filename: Fortune and Fame 5.6\nDoesn't have a Ye verse. Samples \"Something We All Adore\" - <PERSON><PERSON><PERSON> and a Tupac interview.", "length": "161.18", "fileDate": 17226432, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/32b6f40ce7b7cf2c901df04456730a62", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/32b6f40ce7b7cf2c901df04456730a62\", \"key\": \"FORTUNE AND FAME\", \"title\": \"FORTUNE AND FAME [V2]\", \"description\": \"OG Filename: Fortune and Fame 5.6\\nDoesn't have a Ye verse. Samples \\\"Something We All Adore\\\" - <PERSON><PERSON><PERSON> and a Tu<PERSON>c interview.\", \"date\": 17226432, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"533718354282a226301af436fbd1bc39\", \"url\": \"https://api.pillowcase.su/api/download/533718354282a226301af436fbd1bc39\", \"size\": \"3.04 MB\", \"duration\": 161.18}", "aliases": [], "size": "3.04 MB"}, {"id": "fortune-and-fame-118", "name": "FORTUNE AND FAME [V4]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Has alternate Cruza production. Snippet leaked November 30th, 2024.", "length": "7.16", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1581cb261fed521f870d9c3a8d910d17", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1581cb261fed521f870d9c3a8d910d17\", \"key\": \"FORTUNE AND FAME\", \"title\": \"FORTUNE AND FAME [V4]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"Has alternate <PERSON><PERSON> production. Snippet leaked November 30th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"76aed70e51b07b1fd579e4126d7f6a7f\", \"url\": \"https://api.pillowcase.su/api/download/76aed70e51b07b1fd579e4126d7f6a7f\", \"size\": \"572 kB\", \"duration\": 7.16}", "aliases": [], "size": "572 kB"}, {"id": "fortune-and-fame-119", "name": "FORTUNE AND FAME [V5]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Has alternate production from Wes <PERSON> & taydex. Said to be around 5 minutes long. Snippet leaked January 3rd, 2025.", "length": "25.89", "fileDate": 17358624, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1fbb55e0faf58fa81e1a833228eba3a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1fbb55e0faf58fa81e1a833228eba3a0\", \"key\": \"FORTUNE AND FAME\", \"title\": \"FORTUNE AND FAME [V5]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"description\": \"Has alternate production from Wes <PERSON>man & taydex. Said to be around 5 minutes long. Snippet leaked January 3rd, 2025.\", \"date\": 17358624, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b40c73f1b1365401dc03bd960b75457c\", \"url\": \"https://api.pillowcase.su/api/download/b40c73f1b1365401dc03bd960b75457c\", \"size\": \"872 kB\", \"duration\": 25.89}", "aliases": [], "size": "872 kB"}, {"id": "fried-120", "name": "FRIED [V16]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "OG Filename: FRIED 75bpm 5.6\nHas a different structure and an alternate Ty Dolla $ign verse with alternate explicit lines, including a line in support of <PERSON><PERSON> that would later be cut. Has some OG Hooligans near the end of the song.", "length": "179.2", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1528cad69a3a765eb8eff381ed9988c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1528cad69a3a765eb8eff381ed9988c0\", \"key\": \"FRIED\", \"title\": \"FRIED [V16]\", \"artists\": \"(feat. The Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"OG Filename: FRIED 75bpm 5.6\\nHas a different structure and an alternate Ty Dolla $ign verse with alternate explicit lines, including a line in support of <PERSON><PERSON> that would later be cut. Has some OG Hooligans near the end of the song.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e9c9502a20c8e831bf28b2df830ae7a9\", \"url\": \"https://api.pillowcase.su/api/download/e9c9502a20c8e831bf28b2df830ae7a9\", \"size\": \"3.33 MB\", \"duration\": 179.2}", "aliases": ["BLEED IT"], "size": "3.33 MB"}, {"id": "fried-121", "name": "FRIED [V17]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Has alternate Cruza production. Snippet leaked November 5th, 2024.", "length": "10.32", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/8ce6ee557347a2781d031753fc4d1295", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ce6ee557347a2781d031753fc4d1295\", \"key\": \"FRIED\", \"title\": \"FRIED [V17]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Has alternate Cruza production. Snippet leaked November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2590ec98cdfb27e8ba3b5ddd25a3a646\", \"url\": \"https://api.pillowcase.su/api/download/2590ec98cdfb27e8ba3b5ddd25a3a646\", \"size\": \"622 kB\", \"duration\": 10.32}", "aliases": ["BLEED IT"], "size": "622 kB"}, {"id": "fried-122", "name": "FRIED [V19]", "artists": ["The Hooligans"], "producers": ["Digital Nas", "Ojivolta", "TheLabCook"], "notes": "Played by mistake at the release VULTURES 2 LP. Has mixing and production differencies compared to other versions.", "length": "86.41", "fileDate": 17226432, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/97e5a9fe0bf9a019fc482865faedcac1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/97e5a9fe0bf9a019fc482865faedcac1\", \"key\": \"FRIED\", \"title\": \"FRIED [V19]\", \"artists\": \"(feat. <PERSON> Hooligans) (prod. Digital Nas, Ojivolta & TheLabCook)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Played by mistake at the release VULTURES 2 LP. Has mixing and production differencies compared to other versions.\", \"date\": 17226432, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"468f32a89ea68378dadbbd9501bb4847\", \"url\": \"https://api.pillowcase.su/api/download/468f32a89ea68378dadbbd9501bb4847\", \"size\": \"1.84 MB\", \"duration\": 86.41}", "aliases": ["BLEED IT"], "size": "1.84 MB"}, {"id": "fried-123", "name": "✨ FRIED [V20]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "OG Filename: FRIED Wes x Tay Edit 1\nUnknown when exactly when made. Has more Ty$ adlibs that are different to the ones that were on a release version and completely different production. This version is clean.", "length": "179.2", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/dd26c9e6d27e0f4e30f059776629b061", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd26c9e6d27e0f4e30f059776629b061\", \"key\": \"FRIED\", \"title\": \"\\u2728 FRIED [V20]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"BLEED IT\"], \"description\": \"OG Filename: FRIED Wes x Tay Edit 1\\nUnknown when exactly when made. Has more Ty$ adlibs that are different to the ones that were on a release version and completely different production. This version is clean.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"134f21953e848ef6d2b47a913bba0062\", \"url\": \"https://api.pillowcase.su/api/download/134f21953e848ef6d2b47a913bba0062\", \"size\": \"3.33 MB\", \"duration\": 179.2}", "aliases": ["BLEED IT"], "size": "3.33 MB"}, {"id": "fried-124", "name": "FRIED [V21]", "artists": ["The Hooligans"], "producers": ["<PERSON><PERSON>", "TheLabCook", "Digital Nas", "Ojivolta", "IRKO"], "notes": "Played at the Korea LP, <PERSON> <PERSON>'s \"boy don't play with me you know I'm fried\" line seemingly in higher quality not seen in any released mix. Higher quality rip with less crowd noise later leaked.", "length": "166.63", "fileDate": 17259264, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ae76b52207a6ef3979cc2663208fd6fe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae76b52207a6ef3979cc2663208fd6fe\", \"key\": \"FRIED\", \"title\": \"FRIED [V21]\", \"artists\": \"(feat. <PERSON> Hooligans) (prod. <PERSON>, TheLabCook, Digital Nas, Ojivolta & IRKO)\", \"aliases\": [\"BLEED IT\"], \"description\": \"Played at the Korea LP, <PERSON>'s \\\"boy don't play with me you know I'm fried\\\" line seemingly in higher quality not seen in any released mix. Higher quality rip with less crowd noise later leaked.\", \"date\": 17259264, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"deafad2ce6f8f29000028400d6f7cab6\", \"url\": \"https://api.pillowcase.su/api/download/deafad2ce6f8f29000028400d6f7cab6\", \"size\": \"3.12 MB\", \"duration\": 166.63}", "aliases": ["BLEED IT"], "size": "3.12 MB"}, {"id": "fried-125", "name": "FRIED [V22]", "artists": ["The Hooligans"], "producers": ["<PERSON><PERSON>", "TheLabCOok", "Digital Nas", "Ojivolta", "IRKO"], "notes": "Played at the first Haikou LP. Incorporates a saxaphone, which is not present on the released version.", "length": "191.74", "fileDate": 17263584, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d1546d750ac346a6ad2989605f46ee8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1546d750ac346a6ad2989605f46ee8d\", \"key\": \"FRIED\", \"title\": \"FRIED [V22]\", \"artists\": \"(feat. The Hooligans) (prod<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Digital Nas, Ojivolta & IRKO)\", \"description\": \"Played at the first Haikou LP. Incorporates a saxaphone, which is not present on the released version.\", \"date\": 17263584, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ab4c522b167daa5226a1d9ebda72221c\", \"url\": \"https://api.pillowcase.su/api/download/ab4c522b167daa5226a1d9ebda72221c\", \"size\": \"3.53 MB\", \"duration\": 191.74}", "aliases": [], "size": "3.53 MB"}, {"id": "ghost-town", "name": "Ghost Town [V8]", "artists": ["070 Shake", "<PERSON>"], "producers": ["Kanye West", "MIKE DEAN", "<PERSON>", "the Lights", "benny blanco"], "notes": "Version of \"Ghost Town\" played at the 2nd Haikou LP, where PARTYNEXTDOOR's vocals are replaced with <PERSON>.", "length": "242.96", "fileDate": 17274816, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ea268bc46751eeefd84796a01391cd51", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ea268bc46751eeefd84796a01391cd51\", \"key\": \"Ghost Town\", \"title\": \"Ghost Town [V8]\", \"artists\": \"(feat. 070 <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & the Lights & benny blanco)\", \"description\": \"Version of \\\"Ghost Town\\\" played at the 2nd Haikou LP, where PARTYNEXTDOOR's vocals are replaced with <PERSON>.\", \"date\": 17274816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5946c3e4ae40179b92092d8f583ebf8e\", \"url\": \"https://api.pillowcase.su/api/download/5946c3e4ae40179b92092d8f583ebf8e\", \"size\": \"4.34 MB\", \"duration\": 242.96}", "aliases": [], "size": "4.34 MB"}, {"id": "let-go", "name": "⭐ LET GO [V14]", "artists": [], "producers": ["Ojivolta", "Digital Nas"], "notes": "OG Filename: let go - r3\nVersion brought back after VULTURES 2 was reworked in early May. Contains re-recorded <PERSON> vocals and different production.", "length": "174.65", "fileDate": 17290368, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/f7237e16a7f72f460678177fb675e422", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f7237e16a7f72f460678177fb675e422\", \"key\": \"LET GO\", \"title\": \"\\u2b50 LET GO [V14]\", \"artists\": \"(prod. Ojivolta & Digital Nas)\", \"aliases\": [\"I Let Go\"], \"description\": \"OG Filename: let go - r3\\nVersion brought back after VULTURES 2 was reworked in early May. Contains re-recorded Ty vocals and different production.\", \"date\": 17290368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e2b47eee91683f461a09387190c98f59\", \"url\": \"https://api.pillowcase.su/api/download/e2b47eee91683f461a09387190c98f59\", \"size\": \"3.25 MB\", \"duration\": 174.65}", "aliases": ["I Let Go"], "size": "3.25 MB"}, {"id": "let-go-128", "name": "LET GO [V16]", "artists": ["<PERSON> Moose"], "producers": ["Ojivolta", "Digital Nas"], "notes": "OG Filename: LET GO - moose adds 2\nVersion with a feature from Young Moose.", "length": "171.6", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d1683ceb276aaa5ede67f7f7fb17171d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1683ceb276aaa5ede67f7f7fb17171d\", \"key\": \"LET GO\", \"title\": \"LET GO [V16]\", \"artists\": \"(feat. <PERSON>) (prod. Ojivolta & Digital Nas)\", \"aliases\": [\"I Let Go\"], \"description\": \"OG Filename: <PERSON><PERSON> <PERSON> - moose adds 2\\nVersion with a feature from <PERSON> Moose.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"71daa28a62417e79cb263cbde475501f\", \"url\": \"https://api.pillowcase.su/api/download/71daa28a62417e79cb263cbde475501f\", \"size\": \"3.2 MB\", \"duration\": 171.6}", "aliases": ["I Let Go"], "size": "3.2 MB"}, {"id": "let-go-129", "name": "LET GO [V17]", "artists": [], "producers": ["Ojivolta", "Digital Nas", "<PERSON><PERSON>"], "notes": "Has alternate production from Cruza. Snippet leaked November 14th, 2024.", "length": "10", "fileDate": 17306784, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/336a641fbf731182ad2a8d4d01c34276", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/336a641fbf731182ad2a8d4d01c34276\", \"key\": \"LET GO\", \"title\": \"LET GO [V17]\", \"artists\": \"(prod. Ojivolta, Digital Nas & Cruza)\", \"aliases\": [\"I Let Go\"], \"description\": \"Has alternate production from Cruza. Snippet leaked November 14th, 2024.\", \"date\": 17306784, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"5baf92d6c658ffd1004122985adff3a8\", \"url\": \"https://api.pillowcase.su/api/download/5baf92d6c658ffd1004122985adff3a8\", \"size\": \"618 kB\", \"duration\": 10}", "aliases": ["I Let Go"], "size": "618 kB"}, {"id": "let-go-130", "name": "LET GO [V18]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Has alternate production from Wes <PERSON> & taydex. Snippet leaked January 6th, 2025.", "length": "14.13", "fileDate": 17361216, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/eedcdf13a58279a5b605fedc51be069b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eedcdf13a58279a5b605fedc51be069b\", \"key\": \"LET GO\", \"title\": \"LET GO [V18]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"I Let Go\"], \"description\": \"Has alternate production from Wes <PERSON>man & taydex. Snippet leaked January 6th, 2025.\", \"date\": 17361216, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0aac4d680a48df5e94777fb71b1bd93f\", \"url\": \"https://api.pillowcase.su/api/download/0aac4d680a48df5e94777fb71b1bd93f\", \"size\": \"683 kB\", \"duration\": 14.13}", "aliases": ["I Let Go"], "size": "683 kB"}, {"id": "next-time", "name": "✨ NEXT TIME [V3]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: NEXT TIME X MOOSE REF 1\nVersion with a finished <PERSON> hook, and two reference Young Moose verses. This version also uses a slightly different version of leaked beat.", "length": "221.26", "fileDate": 17321472, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/24e89b4712cdba379b0f4664d24be719", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/24e89b4712cdba379b0f4664d24be719\", \"key\": \"NEXT TIME\", \"title\": \"\\u2728 NEXT TIME [V3]\", \"artists\": \"(ref. <PERSON>) (prod. London on da Track)\", \"aliases\": [\"MAKE IT FEEL RIGHT\"], \"description\": \"OG Filename: NEXT TIME X MOOSE REF 1\\nVersion with a finished Ye hook, and two reference Young Moose verses. This version also uses a slightly different version of leaked beat.\", \"date\": 17321472, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0c20bae2152c0515d1dc7cabe8739c34\", \"url\": \"https://api.pillowcase.su/api/download/0c20bae2152c0515d1dc7cabe8739c34\", \"size\": \"4 MB\", \"duration\": 221.26}", "aliases": ["MAKE IT FEEL RIGHT"], "size": "4 MB"}, {"id": "maybe-we-can-last-forever", "name": "MAYBE WE CAN LAST FOREVER [V2]", "artists": ["<PERSON> Moose"], "producers": [], "notes": "OG Filename: 20240516 MAYBE WE CAN LAST FOREVER MOOSE ROUGH EDWIN\nEarly version, with <PERSON> vocals. Has open and different production to release. Clean. Leaked as a blind bonus for the third VULTURES FOR CHARITY groupbuy.", "length": "243.34", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/23efcb0f31259cc096f403ca3ec0707c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23efcb0f31259cc096f403ca3ec0707c\", \"key\": \"MAYBE WE CAN LAST FOREVER\", \"title\": \"MAYBE WE CAN LAST FOREVER [V2]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"FOREVER\", \"<PERSON>Y<PERSON>\"], \"description\": \"OG Filename: 20240516 MAYBE WE CAN LAST FOREVER MOOSE ROUGH EDWIN\\nEarly version, with <PERSON> vocals. Has open and different production to release. Clean. Leaked as a blind bonus for the third VULTURES FOR CHARITY groupbuy.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a6cfdb98193346b72c7a0151cbf72c03\", \"url\": \"https://api.pillowcase.su/api/download/a6cfdb98193346b72c7a0151cbf72c03\", \"size\": \"4.35 MB\", \"duration\": 243.34}", "aliases": ["FOREVER", "MAYBE"], "size": "4.35 MB"}, {"id": "maybe-133", "name": "MAYBE [V4]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Version with production from Cruza. Snippet leaked November 29th, 2024.", "length": "6.55", "fileDate": 17328384, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/9cd9fde7f11b1f6b1969f9c4e0249f4a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9cd9fde7f11b1f6b1969f9c4e0249f4a\", \"key\": \"MAYBE\", \"title\": \"<PERSON>Y<PERSON> [V4]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Maybe\", \"Maybe We Can Last Forever\"], \"description\": \"Version with production from Cruza. Snippet leaked November 29th, 2024.\", \"date\": 17328384, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a0813afb94953972c12cd295e2cdc588\", \"url\": \"https://api.pillowcase.su/api/download/a0813afb94953972c12cd295e2cdc588\", \"size\": \"562 kB\", \"duration\": 6.55}", "aliases": ["Maybe", "Maybe We Can Last Forever"], "size": "562 kB"}, {"id": "my-soul-134", "name": "MY SOUL [V25] ", "artists": ["<PERSON> Moose", "<PERSON>"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: 20240517 MY SOUL MOOSE ROUGH EDWIN\nHas a feature from Young Moose, and is censored.", "length": "170.23", "fileDate": 17394912, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/6a5f440067cd01d1286eecf79d79ae58", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6a5f440067cd01d1286eecf79d79ae58\", \"key\": \"MY SOUL\", \"title\": \"MY SOUL [V25] \", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. BoogzDaBeast & FnZ) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"OG Filename: 20240517 MY SOUL MOOSE ROUGH EDWIN\\nHas a feature from <PERSON> Moose, and is censored.\", \"date\": 17394912, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e7ac08d3b60c2e6a84934b4c1555d0c3\", \"url\": \"https://api.pillowcase.su/api/download/e7ac08d3b60c2e6a84934b4c1555d0c3\", \"size\": \"3.18 MB\", \"duration\": 170.23}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "3.18 MB"}, {"id": "my-soul-135", "name": "🗑️ MY SOUL [V26] ", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: MY SOUL P2 92 (CRUZAFIED V1)\nVersion with completely different production, done by <PERSON><PERSON>. The fact that it is clean means this was most likely made in May when the album was censored.", "length": "146.09", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/a7491ae59d7c05affb070ecdf71eb8a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7491ae59d7c05affb070ecdf71eb8a5\", \"key\": \"MY SOUL\", \"title\": \"\\ud83d\\uddd1\\ufe0f MY SOUL [V26] \", \"artists\": \"(prod. <PERSON><PERSON>) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"OG Filename: MY SOUL P2 92 (CRUZAFIED V1)\\nVersion with completely different production, done by <PERSON><PERSON>. The fact that it is clean means this was most likely made in May when the album was censored.\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f10181561a8666e24a1bd04c58dca390\", \"url\": \"https://api.pillowcase.su/api/download/f10181561a8666e24a1bd04c58dca390\", \"size\": \"2.8 MB\", \"duration\": 146.09}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "2.8 MB"}, {"id": "my-soul-136", "name": "MY SOUL [V27]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>"], "notes": "Another Cruza produced version, now with vocals from <PERSON><PERSON>'s vocalist <PERSON>. Snippet leaked November 5th, 2024.", "length": "9.26", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d0c6b3ac4b7be6dbd5febefb52b659fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d0c6b3ac4b7be6dbd5febefb52b659fb\", \"key\": \"MY SOUL\", \"title\": \"MY SOUL [V27]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"Another Cruza produced version, now with vocals from <PERSON><PERSON>'s vocalist <PERSON>. Snippet leaked November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"34faf63b14332cfa8189c2f2ab3f124a\", \"url\": \"https://api.pillowcase.su/api/download/34faf63b14332cfa8189c2f2ab3f124a\", \"size\": \"606 kB\", \"duration\": 9.26}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "606 kB"}, {"id": "my-soul-137", "name": "MY SOUL [V28]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Has production from Wes Singerman & taydex. Snippet leaked January 7th, 2025.", "length": "16.09", "fileDate": 17362080, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/b52470856f836ad1ec60c802563f4a5f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b52470856f836ad1ec60c802563f4a5f\", \"key\": \"MY SOUL\", \"title\": \"MY SOUL [V28]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"Has production from Wes Singerman & taydex. Snippet leaked January 7th, 2025.\", \"date\": 17362080, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3304c449e9042dfcdadeef98caf991b5\", \"url\": \"https://api.pillowcase.su/api/download/3304c449e9042dfcdadeef98caf991b5\", \"size\": \"715 kB\", \"duration\": 16.09}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "715 kB"}, {"id": "fighting-fires-138", "name": "Big Sean - FIGHTING FIRES [V29]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "Version with vocals from <PERSON>, that he originally played himself on an IG Live, however he said that he didn't know if <PERSON> and <PERSON> were still going to use it for VULTURES 2, <PERSON> later confirmed that it was actually his song but he couldn't clear it in time. Samples Galaxy 2 Galaxy - \"Transition\". Was posted to Big Sean's Soundcloud on August 29th, 2024.", "length": "273.98", "fileDate": 17248896, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/69bb9270c38d08e0c73042c27766ac04", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/69bb9270c38d08e0c73042c27766ac04\", \"key\": \"FIGHTING FIRES\", \"title\": \"<PERSON> Sean - FIGHTING FIRES [V29]\", \"artists\": \"(prod. BoogzDaBeast & FnZ) \", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"MY SOUL\"], \"description\": \"Version with vocals from <PERSON>, that he originally played himself on an IG Live, however he said that he didn't know if <PERSON> and <PERSON> were still going to use it for VULTURES 2, <PERSON> later confirmed that it was actually his song but he couldn't clear it in time. Samples Galaxy 2 Galaxy - \\\"Transition\\\". Was posted to Big Sean's Soundcloud on August 29th, 2024.\", \"date\": 17248896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cfbe54ca8dac604247f523398f53eb7c\", \"url\": \"https://api.pillowcase.su/api/download/cfbe54ca8dac604247f523398f53eb7c\", \"size\": \"4.84 MB\", \"duration\": 273.98}", "aliases": ["Faithful", "Fightin Fire", "MY SOUL"], "size": "4.84 MB"}, {"id": "my-soul-139", "name": "MY SOUL [V30]", "artists": ["<PERSON>"], "producers": ["BoogzDaBeast", "FnZ", "88-<PERSON>"], "notes": "OG Filename: MY_SOUL_8.2.24\nProperly mixed version, likely the one featured on the OG yeezy.com tracklist. Lacks the outro.", "length": "138.19", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/6e6c84badd6864e3e1315a97d1671f3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e6c84badd6864e3e1315a97d1671f3c\", \"key\": \"MY SOUL\", \"title\": \"MY SOUL [V30]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FnZ & 88-<PERSON>)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"OG Filename: MY_SOUL_8.2.24\\nProperly mixed version, likely the one featured on the OG yeezy.com tracklist. Lacks the outro.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"155b9824b225d51062e628b47f39c217\", \"url\": \"https://api.pillowcase.su/api/download/155b9824b225d51062e628b47f39c217\", \"size\": \"2.67 MB\", \"duration\": 138.19}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "2.67 MB"}, {"id": "my-soul-140", "name": "MY SOUL [V31]", "artists": ["<PERSON>", "Big TC"], "producers": ["BoogzDaBeast", "FnZ", "88-<PERSON>"], "notes": "Version of \"My Soul\" played at the VULTURES 2 release LP, features mixed vocals in both ears and samples a prison door sound for the drums during <PERSON> TC's part. <PERSON> <PERSON>'s verse going first instead of <PERSON>'s.", "length": "236.77", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/1863c665f21a72cf21aafeeb2417fadf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1863c665f21a72cf21aafeeb2417fadf\", \"key\": \"MY SOUL\", \"title\": \"MY SOUL [V31]\", \"artists\": \"(feat. <PERSON> & <PERSON>C) (prod. <PERSON><PERSON><PERSON><PERSON>, FnZ & 88-Keys)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"Fighting Fires\"], \"description\": \"Version of \\\"My Soul\\\" played at the VULTURES 2 release LP, features mixed vocals in both ears and samples a prison door sound for the drums during Big TC's part. Has <PERSON>'s verse going first instead of <PERSON>'s.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"023d8bfade591226738d03919af3ab8c\", \"url\": \"https://api.pillowcase.su/api/download/023d8bfade591226738d03919af3ab8c\", \"size\": \"4.25 MB\", \"duration\": 236.77}", "aliases": ["Faithful", "Fightin Fire", "Fighting Fires"], "size": "4.25 MB"}, {"id": "nights-on-the-moon", "name": "NIGHTS ON THE MOON [V2]", "artists": ["<PERSON> Moose"], "producers": ["88-<PERSON>", "<PERSON><PERSON>", "Digital Nas", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON> Drums X NIGHTS ON THE MOON\nPosted by <PERSON> on Instagram as a part of his CJ1's shoe restock.", "length": "190.49", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/96ffa529e3288f14c0219b01e21347bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/96ffa529e3288f14c0219b01e21347bb\", \"key\": \"NIGHTS ON THE MOON\", \"title\": \"NIGHTS ON THE MOON [V2]\", \"artists\": \"(feat. <PERSON>) (prod. 88-<PERSON>, <PERSON>, Digital Nas & Hubi)\", \"description\": \"OG Filename: Moose No Drums X NIGHTS ON THE MOON\\nPosted by <PERSON> on Instagram as a part of his CJ1's shoe restock.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"83ce63b752f6a1765150128a12a2b7b4\", \"url\": \"https://api.pillowcase.su/api/download/83ce63b752f6a1765150128a12a2b7b4\", \"size\": \"3.51 MB\", \"duration\": 190.49}", "aliases": [], "size": "3.51 MB"}, {"id": "pay-per-view-142", "name": "PAY PER VIEW [V10]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>"], "notes": "OG Filename: PPV X MOOSE REF 1\nHas <PERSON> going back and forth with <PERSON> Moose.", "length": "169.46", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/b5a9ba72f95e31ff100159c8dbe5319c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b5a9ba72f95e31ff100159c8dbe5319c\", \"key\": \"PAY PER VIEW\", \"title\": \"PAY PER VIEW [V10]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON>y J & <PERSON>)\", \"description\": \"OG Filename: PPV X MOOSE REF 1\\nHas Ye going back and forth with <PERSON>.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"985de79926b6564312055bce0149fc88\", \"url\": \"https://api.pillowcase.su/api/download/985de79926b6564312055bce0149fc88\", \"size\": \"3.17 MB\", \"duration\": 169.46}", "aliases": [], "size": "3.17 MB"}, {"id": "pay-per-view-143", "name": "✨ PAY PER VIEW [V11]", "artists": ["Project Pat"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "DJ <PERSON>"], "notes": "OG Filename: PPV X MOOSE REF 2\nVersion with <PERSON> Moose reference vocals and further developed <PERSON> vocals. Was privately sold by Luit before being leaked. Has lines that would be reused for \"Husband\".", "length": "169.46", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/bf36f293055a7d5a05e4d29054170ed7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bf36f293055a7d5a05e4d29054170ed7\", \"key\": \"PAY PER VIEW\", \"title\": \"\\u2728 PAY PER VIEW [V11]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON>y J & DJ <PERSON>)\", \"description\": \"OG Filename: PPV X MOOSE REF 2\\nVersion with <PERSON> reference vocals and further developed Ye vocals. Was privately sold by Luit before being leaked. Has lines that would be reused for \\\"Husband\\\".\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3b669f1d4801c362b9a479c82ecb9d54\", \"url\": \"https://api.pillowcase.su/api/download/3b669f1d4801c362b9a479c82ecb9d54\", \"size\": \"3.17 MB\", \"duration\": 169.46}", "aliases": [], "size": "3.17 MB"}, {"id": "promotion-144", "name": "PROMOTION [V19]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "OG Filename: PROMOTION - 5.6.24\nVersion of \"Promotion\" that was still in consideration for VULTURES 2 post-rework, and had a re-recorded Ty Dolla $ign verse to fit the more clean sound. <PERSON>'s vocals also have no autotune after the intro and are left uncensored just like <PERSON>'s. Leaked after <PERSON><PERSON> played it on stream July 3rd, 2024.", "length": "159.07", "fileDate": 17216928, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/27d66d88c2bc3945da7398ed2c0feff8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/27d66d88c2bc3945da7398ed2c0feff8\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V19]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"OG Filename: PROMOTION - 5.6.24\\nVersion of \\\"Promotion\\\" that was still in consideration for VULTURES 2 post-rework, and had a re-recorded Ty Dolla $ign verse to fit the more clean sound. <PERSON>'s vocals also have no autotune after the intro and are left uncensored just like <PERSON>'s. Leaked after <PERSON><PERSON> played it on stream July 3rd, 2024.\", \"date\": 17216928, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f40fcdf0b7c9d91f68f67adb0859c7c6\", \"url\": \"https://api.pillowcase.su/api/download/f40fcdf0b7c9d91f68f67adb0859c7c6\", \"size\": \"3 MB\", \"duration\": 159.07}", "aliases": ["GORGEOUS"], "size": "3 MB"}, {"id": "river-145", "name": "✨ RIVER [V28]", "artists": ["<PERSON> Thug"], "producers": ["AyoAA", "Digital Nas", "London on da Track"], "notes": "OG Filename: river - r2\nHas re-recorded vocals from <PERSON>ign. The sample also has been changed probably to avoid lawsuits. The vocals and sample were reverted back to the previous versions on release.", "length": "247.12", "fileDate": 17290368, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/f56b2512a47c5e738f32f5c722a34c30", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f56b2512a47c5e738f32f5c722a34c30\", \"key\": \"RIVER\", \"title\": \"\\u2728 RIVER [V28]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. <PERSON><PERSON><PERSON>, Digital Nas & London on da Track)\", \"description\": \"OG Filename: river - r2\\nHas re-recorded vocals from <PERSON>ign. The sample also has been changed probably to avoid lawsuits. The vocals and sample were reverted back to the previous versions on release.\", \"date\": 17290368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c473c201568ce418c3f46e639665034\", \"url\": \"https://api.pillowcase.su/api/download/5c473c201568ce418c3f46e639665034\", \"size\": \"4.42 MB\", \"duration\": 247.12}", "aliases": [], "size": "4.42 MB"}, {"id": "river-146", "name": "RIVER [V29]", "artists": ["<PERSON> Thug"], "producers": ["<PERSON><PERSON>"], "notes": "Version with alternate production from Cruza. Has a new and unused outro by <PERSON><PERSON> leaked November 5th, 2024.", "length": "14.54", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ea2587933a830bfcc4cde4ddc11d32c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ea2587933a830bfcc4cde4ddc11d32c3\", \"key\": \"RIVER\", \"title\": \"RIVER [V29]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"Version with alternate production from <PERSON><PERSON>. Has a new and unused outro by <PERSON>. Snippet leaked November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d490dbb20336f8eae3e20961d2869790\", \"url\": \"https://api.pillowcase.su/api/download/d490dbb20336f8eae3e20961d2869790\", \"size\": \"690 kB\", \"duration\": 14.54}", "aliases": [], "size": "690 kB"}, {"id": "river-147", "name": "RIVER [V30]", "artists": ["<PERSON> Thug"], "producers": ["<PERSON><PERSON>"], "notes": "Another <PERSON><PERSON> produced version. Has more new unused Ty vocals. Snippet leaked December 11th, 2024.", "length": "16.87", "fileDate": 17338752, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/20856d1a6945a413aa4a5af096990528", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20856d1a6945a413aa4a5af096990528\", \"key\": \"RIVER\", \"title\": \"RIVER [V30]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"Another Cruza produced version. Has more new unused Ty vocals. Snippet leaked December 11th, 2024.\", \"date\": 17338752, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"890d84937dbb4823a8aa0b910774337e\", \"url\": \"https://api.pillowcase.su/api/download/890d84937dbb4823a8aa0b910774337e\", \"size\": \"727 kB\", \"duration\": 16.87}", "aliases": [], "size": "727 kB"}, {"id": "river-148", "name": "RIVER [V31]", "artists": ["<PERSON> Thug"], "producers": ["<PERSON>", "taydex"], "notes": "Has alternate production from Wes <PERSON> & taydex. Snippet leaked January 6th, 2025.", "length": "29.96", "fileDate": 17361216, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/793b81ed35ad5213785f8dcb6539bdeb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/793b81ed35ad5213785f8dcb6539bdeb\", \"key\": \"RIVER\", \"title\": \"RIVER [V31]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> & taydex)\", \"description\": \"Has alternate production from <PERSON> & taydex. Snippet leaked January 6th, 2025.\", \"date\": 17361216, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"63cfba1540315fb899ca61b3fd2aabae\", \"url\": \"https://api.pillowcase.su/api/download/63cfba1540315fb899ca61b3fd2aabae\", \"size\": \"937 kB\", \"duration\": 29.96}", "aliases": [], "size": "937 kB"}, {"id": "slide-149", "name": "SLIDE [V32]", "artists": ["<PERSON> Moose"], "producers": ["<PERSON> again..", "<PERSON>"], "notes": "OG Filename: 20240518 SLIDE MOOSE ROUGH EDWIN \nHas a verse from <PERSON>.", "length": "315.56", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/a8e92fa649fd7f2ce43bf4813f899108", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a8e92fa649fd7f2ce43bf4813f899108\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V32]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> again.. & <PERSON>)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"OG Filename: 20240518 SLIDE MOOSE ROUGH EDWIN \\nHas a verse from <PERSON>.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6c258913be4cb2f145d5dc18a2afcb80\", \"url\": \"https://api.pillowcase.su/api/download/6c258913be4cb2f145d5dc18a2afcb80\", \"size\": \"5.51 MB\", \"duration\": 315.56}", "aliases": ["Slide In", "SLIDIN"], "size": "5.51 MB"}, {"id": "slidin-150", "name": "SLIDIN [V33]", "artists": [], "producers": ["<PERSON> again..", "<PERSON>"], "notes": "OG Filename: slidin string only sample\nFilename leaked by violinist <PERSON>. Snippet posted by <PERSON> himself on August 4th, 2024. File is 13 seconds long.", "length": "", "fileDate": 17227296, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/cc15469d91efa71e353d4fab2dca9cb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc15469d91efa71e353d4fab2dca9cb0\", \"key\": \"SLIDIN\", \"title\": \"SLIDIN [V33]\", \"artists\": \"(prod. <PERSON> again.. & <PERSON>)\", \"aliases\": [\"Slide In\", \"SLIDE\"], \"description\": \"OG Filename: slidin string only sample\\nFilename leaked by violinist <PERSON>. Snippet posted by <PERSON> himself on August 4th, 2024. File is 13 seconds long.\", \"date\": 17227296, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["Slide In", "SLIDE"], "size": ""}, {"id": "star-time", "name": "STAR TIME [V12]", "artists": ["<PERSON> Moose", "The-Dream"], "producers": ["88-<PERSON>"], "notes": "OG Filename: STAR TIME MOOSE 1\nVersion brought back for VULTURES. Has a Young Moose feature, mumble Ye vocals, and a long outro.", "length": "275.76", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d1170a82e6c0bc5553f232b438e1f016", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1170a82e6c0bc5553f232b438e1f016\", \"key\": \"STAR TIME\", \"title\": \"STAR TIME [V12]\", \"artists\": \"(feat. <PERSON> & <PERSON>-<PERSON>) (prod. 88-Keys)\", \"aliases\": [\"The Mind Is Powerful\", \"Start Time\"], \"description\": \"OG Filename: STAR TIME MOOSE 1\\nVersion brought back for VULTURES. Has a Young Moose feature, mumble Ye vocals, and a long outro.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c69faaaab6e85e6b4c2062cf0284f1ed\", \"url\": \"https://api.pillowcase.su/api/download/c69faaaab6e85e6b4c2062cf0284f1ed\", \"size\": \"4.87 MB\", \"duration\": 275.76}", "aliases": ["The Mind Is Powerful", "Start Time"], "size": "4.87 MB"}, {"id": "throw-away", "name": "THROW AWAY", "artists": [], "producers": [], "notes": "According to <PERSON><PERSON>, there is a solo Ty Dolla $ign song included in a May copy of VULTURES 2. Unknown if <PERSON> ever recorded on it. Samples \"Throw Away\" by Future. Snippet leaked December 16th, 2024.", "length": "10.92", "fileDate": 17343072, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/6fcaff99fe062a8ee86d598d392c0b0f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6fcaff99fe062a8ee86d598d392c0b0f\", \"key\": \"THROW AWAY\", \"title\": \"THROW AWAY\", \"description\": \"According to <PERSON><PERSON>, there is a solo Ty Dolla $ign song included in a May copy of VULTURES 2. Unknown if <PERSON> ever recorded on it. <PERSON><PERSON> \\\"Throw Away\\\" by Future. Snippet leaked December 16th, 2024.\", \"date\": 17343072, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"041df1898cd184b6d6410c503e353ca2\", \"url\": \"https://api.pillowcase.su/api/download/041df1898cd184b6d6410c503e353ca2\", \"size\": \"545 kB\", \"duration\": 10.92}", "aliases": [], "size": "545 kB"}, {"id": "time-moving-slow-153", "name": "TIME MOVING SLOW [V26]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Has alternate production done by <PERSON><PERSON>. Snippet leaked November 5th, 2024.", "length": "9.48", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/e43f49c6aeb3d2c9804fdfd51bfa2ef8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e43f49c6aeb3d2c9804fdfd51bfa2ef8\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V26]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Has alternate production done by Cruza. Snippet leaked November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9a0cd1a963fb08dc73536137346d0b49\", \"url\": \"https://api.pillowcase.su/api/download/9a0cd1a963fb08dc73536137346d0b49\", \"size\": \"609 kB\", \"duration\": 9.48}", "aliases": ["TIME MOVES SLOW"], "size": "609 kB"}, {"id": "time-moving-slow-154", "name": "TIME MOVING SLOW [V27]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Another version with alternate Cruza production. Snippet leaked December 13th, 2024.", "length": "18.43", "fileDate": 17340480, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/331f0d55abbd7fe669782d63f77394a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/331f0d55abbd7fe669782d63f77394a6\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V27]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Another version with alternate Cruza production. Snippet leaked December 13th, 2024.\", \"date\": 17340480, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7f9187cc35545aca62029532d119e793\", \"url\": \"https://api.pillowcase.su/api/download/7f9187cc35545aca62029532d119e793\", \"size\": \"752 kB\", \"duration\": 18.43}", "aliases": ["TIME MOVES SLOW"], "size": "752 kB"}, {"id": "time-moving-slow-155", "name": "TIME MOVING SLOW [V28]", "artists": [], "producers": ["???"], "notes": "Has alternate production from an unknown producer. Snippet leaked November 2nd, 2024.", "length": "26.85", "fileDate": 17305056, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/d7cf257baf50d180a86719aef5dba034", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d7cf257baf50d180a86719aef5dba034\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V28]\", \"artists\": \"(prod. ???)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Has alternate production from an unknown producer. Snippet leaked November 2nd, 2024.\", \"date\": 17305056, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1ba83d965a197680a496032e5d734301\", \"url\": \"https://api.pillowcase.su/api/download/1ba83d965a197680a496032e5d734301\", \"size\": \"887 kB\", \"duration\": 26.85}", "aliases": ["TIME MOVES SLOW"], "size": "887 kB"}, {"id": "time-moving-slow-156", "name": "TIME MOVING SLOW [V29]", "artists": [], "producers": ["<PERSON>", "taydex"], "notes": "Has alternate production from Wes <PERSON> & taydex. Snippet leaked January 6th, 2025.", "length": "12.96", "fileDate": 17361216, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/afdf0ef0d133dd00a2fd0d6b4e13fb91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afdf0ef0d133dd00a2fd0d6b4e13fb91\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V29]\", \"artists\": \"(prod. <PERSON> & taydex)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Has alternate production from Wes <PERSON>man & taydex. Snippet leaked January 6th, 2025.\", \"date\": 17361216, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c9a77c0a498cd7321fd3ea8f7f13b060\", \"url\": \"https://api.pillowcase.su/api/download/c9a77c0a498cd7321fd3ea8f7f13b060\", \"size\": \"665 kB\", \"duration\": 12.96}", "aliases": ["TIME MOVES SLOW"], "size": "665 kB"}, {"id": "time-moving-slow-157", "name": "TIME MOVING SLOW [V30]", "artists": ["<PERSON> Moose"], "producers": ["AyoAA", "<PERSON><PERSON>", "SHDØW"], "notes": "OG Filename: 20240519 TIME MOVING SLOW MOOSE ROUGH EDWIN \nHas a Young Moose verse and new production.", "length": "177", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/f4539867fc1751a8da996f056042d8a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f4539867fc1751a8da996f056042d8a9\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V30]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>ya Man & SHD\\u00d8W)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"OG Filename: 20240519 TIME MOVING SLOW MOOSE ROUGH EDWIN \\nHas a Young Moose verse and new production.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"07475ee870beab1bcd552cbb973a2e7c\", \"url\": \"https://api.pillowcase.su/api/download/07475ee870beab1bcd552cbb973a2e7c\", \"size\": \"3.29 MB\", \"duration\": 177}", "aliases": ["TIME MOVES SLOW"], "size": "3.29 MB"}, {"id": "about-mine", "name": "ABOUT MINE [V7]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless", "???"], "notes": "OG Filename: about mine bump ref\nReference track done by <PERSON><PERSON>. These vocals would actually end up being used, but for the first Young Moose ref version of \"Pay Per View\".", "length": "151.93", "fileDate": 17314560, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/7e686d2aaee3262b7463e4e9202c2b12", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e686d2aaee3262b7463e4e9202c2b12\", \"key\": \"ABOUT MINE\", \"title\": \"ABOUT MINE [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON>, <PERSON><PERSON> & <PERSON>B<PERSON> Never Broke Again) (prod. Bud<PERSON> Bless & ???)\", \"aliases\": [\"YE ABOUT MINE\", \"Let Me Chill Out\", \"MOTION\"], \"description\": \"OG Filename: about mine bump ref\\nReference track done by <PERSON><PERSON>. These vocals would actually end up being used, but for the first <PERSON> Moose ref version of \\\"Pay Per View\\\".\", \"date\": 17314560, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5cf28fc061b52b4214069fd679e1f3df\", \"url\": \"https://api.pillowcase.su/api/download/5cf28fc061b52b4214069fd679e1f3df\", \"size\": \"2.89 MB\", \"duration\": 151.93}", "aliases": ["YE ABOUT MINE", "Let Me Chill Out", "MOTION"], "size": "2.89 MB"}, {"id": "ye-about-mine-159", "name": "YE ABOUT MINE [V8]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless", "???"], "notes": "OG Filename: YE ABOUT MINE X MOOSE ref 1\nVersion with <PERSON> vocals. Seemingly made after March 9th due to the extra strings throughout the song.", "length": "151.82", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/665cca71491cecd9ac0364ae1d7786c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/665cca71491cecd9ac0364ae1d7786c2\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V8]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>, <PERSON><PERSON> & <PERSON>B<PERSON> Never Broke Again) (prod. Bud<PERSON> Bless & ???)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"OG Filename: YE ABOUT MINE X MOOSE ref 1\\nVersion with <PERSON> vocals. Seemingly made after March 9th due to the extra strings throughout the song.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a0365df2aa0d9194969f0fe2f8be88c\", \"url\": \"https://api.pillowcase.su/api/download/4a0365df2aa0d9194969f0fe2f8be88c\", \"size\": \"2.89 MB\", \"duration\": 151.82}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "2.89 MB"}, {"id": "ye-about-mine-160", "name": "YE ABOUT MINE [V9]", "artists": ["<PERSON>", "Takeoff", "<PERSON><PERSON><PERSON> Never Broke Again"], "producers": ["Bud<PERSON> Bless", "???"], "notes": "OG Filename: YE ABOUT MINE X MOOSE ref 2\nAnother version seemingly made after March 9th due to the extra strings throughout the song.", "length": "151.82", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/339bb83f1ec0a9e9da826d06af9610e7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/339bb83f1ec0a9e9da826d06af9610e7\", \"key\": \"YE ABOUT MINE\", \"title\": \"YE ABOUT MINE [V9]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>, <PERSON><PERSON> & <PERSON>B<PERSON> Never Broke Again) (prod. Buddah Bless & ???)\", \"aliases\": [\"Let Me Chill Out\", \"MOTION\"], \"description\": \"OG Filename: YE ABOUT MINE X MOOSE ref 2\\nAnother version seemingly made after March 9th due to the extra strings throughout the song.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"054047bc185efb7d9e19afcfd48a8791\", \"url\": \"https://api.pillowcase.su/api/download/054047bc185efb7d9e19afcfd48a8791\", \"size\": \"2.89 MB\", \"duration\": 151.82}", "aliases": ["Let Me Chill Out", "MOTION"], "size": "2.89 MB"}, {"id": "-161", "name": "???", "artists": [], "producers": [], "notes": "Unknown song. Samples \"Will O' The Wisp\" by <PERSON>. Snippet leaked November 18th, 2024. According to <PERSON>, this was not made for BULLY.", "length": "5.83", "fileDate": 17318880, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/ffa4203cfd8c7389752f5e5f0b1af168", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ffa4203cfd8c7389752f5e5f0b1af168\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Press\"], \"description\": \"Unknown song. Sam<PERSON> \\\"Will O' The Wisp\\\" by <PERSON>. Snippet leaked November 18th, 2024. According to <PERSON>, this was not made for BULLY.\", \"date\": 17318880, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d05902bb58385b436e42eefc2b544a98\", \"url\": \"https://api.pillowcase.su/api/download/d05902bb58385b436e42eefc2b544a98\", \"size\": \"918 kB\", \"duration\": 5.83}", "aliases": ["Press"], "size": "918 kB"}, {"id": "-162", "name": "???", "artists": [], "producers": [], "notes": "The 3rd in a group of 10 freestyles. Snippet leaked November 18th, 2024. Originally thought to be recorded on November 14th, 2024, however <PERSON> has said that this was not made for BULLY. Theorized to have been from the session for \"FRIED\", as the flow is similar to that present on the song.", "length": "6.6", "fileDate": 17318880, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/91d63f6dec6cbb306dbf05638398ccdd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91d63f6dec6cbb306dbf05638398ccdd\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"She Or A He?\"], \"description\": \"The 3rd in a group of 10 freestyles. Snippet leaked November 18th, 2024. Originally thought to be recorded on November 14th, 2024, however <PERSON> has said that this was not made for BULLY. Theorized to have been from the session for \\\"FRIED\\\", as the flow is similar to that present on the song.\", \"date\": 17318880, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5593cb958af8c84c833a06284d9e873a\", \"url\": \"https://api.pillowcase.su/api/download/5593cb958af8c84c833a06284d9e873a\", \"size\": \"977 kB\", \"duration\": 6.6}", "aliases": ["She Or A He?"], "size": "977 kB"}, {"id": "made-it-this-far", "name": "<PERSON><PERSON><PERSON> - Made It This Far [V4]", "artists": ["Vory"], "producers": ["Digital Nas"], "notes": "Previewed on <PERSON><PERSON>'s Instagram telling the fans that he wants <PERSON><PERSON> to drop it. Almost 3 minutes longer than the other versions. Unknown if this was made before or after the TAMPERING WITH SOUND version.", "length": "40.39", "fileDate": 17357760, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/2dde2a1fe73197b6d32e07e3344c62c4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2dde2a1fe73197b6d32e07e3344c62c4\", \"key\": \"Made It This Far\", \"title\": \"<PERSON><PERSON><PERSON> - Made It This Far [V4]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. Digital Nas)\", \"aliases\": [\"Made It\"], \"description\": \"Previewed on <PERSON><PERSON>'s Instagram telling the fans that he wants <PERSON><PERSON> to drop it. Almost 3 minutes longer than the other versions. Unknown if this was made before or after the TAMPERING WITH SOUND version.\", \"date\": 17357760, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8944ab8342bfc0746ff474e7a1d1f374\", \"url\": \"https://api.pillowcase.su/api/download/8944ab8342bfc0746ff474e7a1d1f374\", \"size\": \"1.1 MB\", \"duration\": 40.39}", "aliases": ["Made It"], "size": "1.1 MB"}, {"id": "bomb", "name": "North West - Bomb", "artists": [], "producers": [], "notes": "\"Bomb\" can be heard in one of the footages of <PERSON> in Moscow on June 30th, 2024. Differences are unknown, but North's Japanese vocals are already present. Seen on the leaked Elementary School Dropout tracklist in a now-deleted video from <PERSON>'s YouTube channel.", "length": "8.06", "fileDate": 17197056, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-2", "originalUrl": "https://pillowcase.su/f/883815bce4b9095ac988f7d71ebe3321", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/883815bce4b9095ac988f7d71ebe3321\", \"key\": \"Bomb\", \"title\": \"North West - Bomb\", \"description\": \"\\\"Bomb\\\" can be heard in one of the footages of <PERSON> in Moscow on June 30th, 2024. Differences are unknown, but <PERSON>'s Japanese vocals are already present. Seen on the leaked Elementary School Dropout tracklist in a now-deleted video from Saint's YouTube channel.\", \"date\": 17197056, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"911902e71a537e14e69d6defdfadbdfe\", \"url\": \"https://api.pillowcase.su/api/download/911902e71a537e14e69d6defdfadbdfe\", \"size\": \"587 kB\", \"duration\": 8.06}", "aliases": [], "size": "587 kB"}]}