/**
 * IndexNow Configuration for TrackerHive
 * Handles automatic URL submission to search engines
 */

export const INDEXNOW_CONFIG = {
  // IndexNow API key (matches the filename in public/)
  key: 'a1b2c3d4e5f6g7h8i9j0',
  
  // Your domain
  host: 'aitrackerhive.com',
  
  // IndexNow API endpoint
  endpoint: 'https://api.indexnow.org/indexnow',
  
  // Supported search engines
  searchEngines: [
    'https://api.indexnow.org/indexnow', // Bing & Yandex
  ],
  
  // Rate limiting
  maxUrlsPerSubmission: 100,
  
  // Retry configuration
  retryAttempts: 3,
  retryDelay: 1000, // ms
};

/**
 * Get priority URLs for immediate indexing
 */
export function getPriorityUrls(baseUrl) {
  return [
    baseUrl,
    `${baseUrl}/artists/ye`,
    `${baseUrl}/artists/playboi-carti`,
    `${baseUrl}/artists/ye/unreleased`,
    `${baseUrl}/artists/playboi-carti/unreleased`,
    `${baseUrl}/categories/unreleased`,
    `${baseUrl}/categories/released`,
    `${baseUrl}/categories/art`,
  ];
}

/**
 * Check if URL should be submitted to IndexNow
 */
export function shouldSubmitUrl(url) {
  // Skip certain file types and paths
  const skipPatterns = [
    /\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i,
    /\/api\//,
    /\/admin\//,
    /\/\.well-known\//,
    /\/sitemap/,
    /\/robots\.txt/,
  ];
  
  return !skipPatterns.some(pattern => pattern.test(url));
}

export default INDEXNOW_CONFIG;
