---
import MainLayout from '../../../../layouts/MainLayout.astro';
import { promises as fs } from 'fs';
import path from 'path';
import SmartImage from '../../../../components/SmartImage.astro';
import { t } from '../../../../i18n';

import AlbumGridDisplay from '../../../../components/categoryViews/AlbumGridDisplay.astro';
import TrackListDisplay from '../../../../components/categoryViews/TrackListDisplay.astro';
import ArtworkDisplay from '../../../../components/categoryViews/ArtworkDisplay.astro';
import CollapsibleSubmissionForm from '../../../../components/content-submission/CollapsibleSubmissionForm.astro';
import type { Track } from '../../../../types/Track';
import type { Artwork } from '../../../../types/Artwork';

// 设置葡萄牙语环境
const lang = 'pt';

// 启用预渲染
export const prerender = true;

// 获取所有分类用于静态生成
export async function getStaticPaths() {
  // 在函数内部定义分类列表，确保与 ye.astro 中的列表一致
  const yeCategories = [
    { id: 'album-copies', name: 'Album Copies' },
    { id: 'art', name: 'Art' },
    { id: 'best-of', name: 'Best Of' },
    { id: 'fakes', name: 'Fakes' },
    { id: 'grails', name: 'Grails' },
    { id: 'groupbuys', name: 'Groupbuys' },
    { id: 'misc', name: 'Misc' },
    { id: 'recent', name: 'Recent' },
    { id: 'released', name: 'Released' },
    { id: 'special', name: 'Special' },
    { id: 'stems', name: 'Stems' },
    { id: 'tracklists', name: 'Tracklists' },
    { id: 'unreleased', name: 'Unreleased' },
    { id: 'worst-of', name: 'Worst Of' }
  ];
  
  const paths = [];

  for (const category of yeCategories) {
    paths.push({
      params: { categoryId: category.id },
      props: { categoryName: category.name }
    });
  }

  return paths;
}

// 获取特定分类数据
const { categoryId } = Astro.params;
const { categoryName } = Astro.props;
const artistId = 'ye';

// 定义分类列表（确保与 ye.astro 和 getStaticPaths 中的列表一致）
const allCategories = [
  { id: 'album-copies', name: 'Album Copies' },
  { id: 'art', name: 'Art' },
  { id: 'best-of', name: 'Best Of' },
  { id: 'fakes', name: 'Fakes' },
  { id: 'grails', name: 'Grails' },
  { id: 'groupbuys', name: 'Groupbuys' },
  { id: 'misc', name: 'Misc' },
  { id: 'recent', name: 'Recent' },
  { id: 'released', name: 'Released' },
  { id: 'special', name: 'Special' },
  { id: 'stems', name: 'Stems' },
  { id: 'tracklists', name: 'Tracklists' },
  { id: 'unreleased', name: 'Unreleased' },
  { id: 'worst-of', name: 'Worst Of' }
];

// 面包屑导航数据 - 修正顺序并使用葡萄牙语翻译
const breadcrumbs = [
  { name: t('common.home', lang), url: '/pt' },
  { name: t('common.artists', lang), url: '/pt/artists' },
  { name: 'Ye', url: '/pt/artists/ye' },
  { name: t(`categories.${categoryId}`, lang) || categoryName, url: `/pt/artists/ye/${categoryId}`, current: true }
];

// 从JSON文件加载分类下的专辑数据
let categoryInfo = null;
let albums = [];

try {
  // 尝试加载分类信息
  const categoryInfoPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'info.json');
  
  try {
    const categoryInfoData = await fs.readFile(categoryInfoPath, 'utf-8');
    categoryInfo = JSON.parse(categoryInfoData);
  } catch (infoError) {
    console.error(`Category info not found for ${categoryId}:`, infoError);
  }

  // 从info.json中获取显示模式，默认为'albums'
  const displayMode = categoryInfo?.displayMode || 'albums';

  // 只有在 albums 模式下才加载 eras.json
  if (displayMode === 'albums') {
    try {
      const categoryErasPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');
      const erasData = await fs.readFile(categoryErasPath, 'utf-8');
      albums = JSON.parse(erasData);
      
      // 确保每个专辑都有coverImage和backgroundColor字段，并检查是否有数据
      albums = await Promise.all(albums.map(async (album) => {
        // 检查该 era 是否有实际的数据文件
        let hasData = false;
        try {
          const eraDataPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras', `${album.id}.json`);
          const eraData = await fs.readFile(eraDataPath, 'utf-8');
          const parsedEraData = JSON.parse(eraData);
          hasData = parsedEraData && parsedEraData.length > 0;
        } catch (error) {
          hasData = false;
        }

        return {
          ...album,
          coverImage: album.coverImage || '/images/eras/default.svg',
          backgroundColor: album.backgroundColor || '#333',
          hasData
        };
      }));
      
      // 按照专辑发行顺序排序
      const eraOrder = [
        'before-the-college-dropout',
        'the-college-dropout',
        'late-registration',
        'graduation',
        '808s-heartbreak',
        'good-ass-job',
        'my-beautiful-dark-twisted-fantasy',
        'watch-the-throne',
        'cruel-summer',
        'thank-god-for-drugs',
        'yeezus',
        'cruel-winter-v1',
        'yeezus-2',
        'so-help-me-god',
        'swish',
        'the-life-of-pablo',
        'turbo-grafx-16',
        'cruel-winter-v2',
        'love-everyone',
        'ye',
        'kids-see-ghosts',
        'yandhi-v1',
        'yandhi-v2',
        'jesus-is-king',
        'jesus-is-lord',
        'donda-v1',
        'donda-v2',
        'donda-v3',
        'donda',
        'donda-2',
        'vultures-1',
        'vultures-2',
        'vultures-3',
        'vultures'
      ];
      
      albums.sort((a, b) => {
        let indexA = eraOrder.indexOf(a.id);
        let indexB = eraOrder.indexOf(b.id);
        
        // 如果在预定义顺序中找不到，放到最后
        if (indexA === -1) indexA = 999;
        if (indexB === -1) indexB = 999;
        
        return indexA - indexB;
      });
    } catch (erasError) {
      console.error(`Eras data not found for category ${categoryId}:`, erasError);
    }
  }
} catch (error) {
  console.error(`Error loading category data for ${categoryId}:`, error);
}

// Get display mode from info.json, default to 'albums'
const displayMode = categoryInfo?.displayMode || 'albums';

// Fallback data if JSON loading fails or is empty (for albums)
if (displayMode === 'albums' && (!albums || albums.length === 0)) {
  albums = [
    {
      id: 'the-college-dropout',
      name: 'The College Dropout',
      description: 'Debut studio album',
      trackCount: 21,
      backgroundColor: '#8B4513',
      coverImage: '/images/eras/default.jpg',
      artist: 'Ye',
      artistId: 'ye'
    }
  ];
}

// 尝试加载分类的tracks数据或artwork数据
let tracks: Track[] = [];
let artworks: Artwork[] = [];
// 加载tracks或artworks数据
{
  try {
    const tracksPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'tracks.json');
    const tracksData = await fs.readFile(tracksPath, 'utf-8');
    const parsedData = JSON.parse(tracksData);
    
    if (displayMode === 'flat') {
      tracks = parsedData;
    } else if (displayMode === 'poster') {
      artworks = parsedData;
    }

    // Basic validation or transformation if needed
    if (!Array.isArray(tracks)) {
      console.error(`Invalid tracks data format for ${categoryId}: Expected an array.`);
      tracks = [];
    }
  } catch (tracksError) {
    // 只有在 flat 或 poster 模式下才记录错误，因为 albums 模式不需要 tracks.json
    if (displayMode === 'flat' || displayMode === 'poster') {
      console.log(`Note: No tracks.json found for ${categoryId}, but this is only needed for flat/poster modes.`);
    }
    
    // Special handling for 'recent' category - attempt to parse HTML
    if (categoryId === 'recent') {
      // Placeholder for potential future HTML parsing logic
      // For now, we just log that it's not implemented
      try {
        const recentHtmlPath = path.join(process.cwd(), 'data', 'YeRecent.html');
        // await fs.access(recentHtmlPath); // Check if file exists
        // TODO: Implement HTML parsing logic here if needed
        // const htmlContent = await fs.readFile(recentHtmlPath, 'utf-8');
        // tracks = parseHtmlToTracks(htmlContent); 
        console.log('Found YeRecent.html, but parsing to tracks is not implemented yet.');
      } catch (recentError) {
        console.error('YeRecent.html processing error:', recentError);
      }
    }
  }
}

// Check if category has data
const hasData = ['art', 'best-of', 'recent', 'unreleased'].includes(categoryId);

// Determine which component to render based on displayMode
let ComponentToRender;
if (displayMode === 'albums') {
  ComponentToRender = AlbumGridDisplay;
} else if (displayMode === 'flat') {
  ComponentToRender = TrackListDisplay;
} else if (displayMode === 'poster') {
  ComponentToRender = ArtworkDisplay;
} // Add more else if for other display modes

// 为SEO准备的元数据
const metaDescription = t(`categories.${categoryId}.description`, lang) || 
                       `Explore ${categoryName} from Ye's discography on TrackerHive.`;
const keywords = `Ye, Kanye West, ${categoryName}, music, discography, hip hop, rap`;
---

<MainLayout 
  title={`${t(`categories.${categoryId}`, lang) || categoryName} - Ye - TrackerHive`}
  description={metaDescription}
  keywords={keywords}
  lang={lang}
>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 ? (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
              </svg>
            ) : null}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-text-primary cursor-default' 
                  : 'text-text-secondary hover:text-text-primary'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>

    <!-- 所有分类 -->
    <section class="mb-8">
      <h2 class="text-xl font-bold text-white mb-4">{t('common.categories', lang) || 'Categorias'}</h2>
      <div class="flex flex-wrap gap-2">
        {(() => {
          // 按指定顺序排列有数据的分类
          const orderedCategories = ['unreleased', 'recent', 'best-of', 'art'];
          return [...allCategories].sort((a, b) => {
            const aIndex = orderedCategories.indexOf(a.id);
            const bIndex = orderedCategories.indexOf(b.id);
            
            // 如果两个都在有序列表中，按列表顺序排序
            if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
            
            // 如果只有一个在有序列表中，则它排在前面
            if (aIndex !== -1) return -1;
            if (bIndex !== -1) return 1;
            
            // 如果都不在有序列表中，按字母排序
            return a.name.localeCompare(b.name);
          }).map(category => {
            const hasData = ['unreleased', 'recent', 'best-of', 'art'].includes(category.id);
            const isActive = category.id === categoryId;

            return (
              <a
                href={`/pt/artists/ye/${category.id}`}
                class={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-primary text-white'
                    : hasData
                      ? 'bg-dark text-white hover:bg-dark-hover hover:text-primary border border-primary/20'
                      : 'bg-dark text-text-secondary hover:bg-dark-hover hover:text-text-primary opacity-60'
                }`}
              >
                {t(`categories.${category.id}`, lang) || category.name}
                {hasData && !isActive && <span class="ml-1 text-primary text-xs">●</span>}
              </a>
            );
          })
        })()}
      </div>
    </section>

    <!-- Add Track Section (exclude unreleased category) - Moved to top for better visibility -->
    {categoryId !== 'unreleased' && (
      <CollapsibleSubmissionForm artistId={artistId} categoryId={categoryId} />
    )}

    <!-- 分类内容 -->
    <div class="mb-8">
      {!hasData ? (
        <div class="text-center py-16">
          <div class="max-w-lg mx-auto bg-dark-elevated p-8 rounded-xl border border-dark-hover shadow-lg">
            <div class="flex justify-center mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-white mb-4 text-center">{t('common.comingSoon', lang) || 'Em Breve'}</h2>
            <p class="text-text-secondary text-center">{t('common.contentBeingCurated', lang) || 'Estamos trabalhando na curadoria de conteúdo para a categoria'} <span class="text-primary font-semibold">{t(`categories.${categoryId}`, lang) || categoryName}</span>. {t('common.checkBackSoon', lang) || 'Volte em breve para atualizações!'}</p>
          </div>
        </div>
      ) : ComponentToRender ? (
        displayMode === 'poster' ? (
          <ComponentToRender artworks={artworks} categoryId={categoryId} />
        ) : (
          <ComponentToRender albums={albums} tracks={tracks} categoryId={categoryId} />
        )
      ) : (
        <div class="text-center py-8">
          <p class="text-gray-400">{t('common.displayModeError', lang) || 'Erro: Modo de exibição não configurado ou componente não encontrado.'}</p>
          <p class="text-gray-500 text-sm mt-2">{t('common.displayModeErrorDetail', lang) || 'Não foi possível determinar como exibir o conteúdo para esta categoria.'}</p>
        </div>
      )}
    </div>

  </main>
</MainLayout>
