---
import MainLayout from '../../../../../../layouts/MainLayout.astro';
import { promises as fs } from 'fs';
import path from 'path';
import { t } from '../../../../../../i18n';

// 设置葡萄牙语环境
const lang = 'pt';

// 启用预渲染
export const prerender = true;

// 获取静态路径
export async function getStaticPaths() {
  try {
    const categoriesDir = path.join(process.cwd(), 'public', 'data', 'artists', 'ye', 'categories');
    const categories = await fs.readdir(categoriesDir);
    
    const paths = [];
    
    for (const category of categories) {
      // 跳过非目录项
      const categoryPath = path.join(categoriesDir, category);
      const categoryStats = await fs.stat(categoryPath);
      if (!categoryStats.isDirectory()) continue;
      
      // 读取分类信息
      const categoryInfoPath = path.join(categoryPath, 'info.json');
      let categoryData = { name: category };
      try {
        const categoryInfo = await fs.readFile(categoryInfoPath, 'utf-8');
        categoryData = JSON.parse(categoryInfo);
      } catch (error) {
        console.error(`Error reading category info for ${category}:`, error);
      }
      
      // 读取分类信息中的显示模式
      let displayMode = 'albums'; // 默认为 albums 模式
      
      if (categoryData && categoryData.displayMode) {
        displayMode = categoryData.displayMode;
      }
      
      // 只有在 albums 模式下才尝试读取 eras 目录
      if (displayMode === 'albums') {
        const erasDir = path.join(categoryPath, 'eras');
        
        try {
          // 先检查目录是否存在
          const erasDirStats = await fs.stat(erasDir).catch(() => null);
          
          if (erasDirStats && erasDirStats.isDirectory()) {
            const eras = await fs.readdir(erasDir);
            
            for (const era of eras) {
              if (!era.endsWith('.json')) continue;
              
              const eraId = era.replace('.json', '');
              
              paths.push({
                params: { categoryId: category, eraId },
                props: { categoryName: categoryData.name || category }
              });
            }
          }
        } catch (error) {
          console.error(`Error reading eras for ${category}:`, error);
        }
      }
    }
    
    return paths;
  } catch (error) {
    console.error('Error in getStaticPaths:', error);
    return [];
  }
}

// 获取路径参数
const { categoryId, eraId } = Astro.params;
let { categoryName } = Astro.props;

// 中文分类名称到英文的映射
const categoryNameMap = {
  '未发行': 'Unreleased',
  '专辑': 'Albums',
  '合作': 'Collaborations',
  '单曲': 'Singles'
};

// 如果有映射，使用英文名称
if (categoryNameMap[categoryName]) {
  categoryName = categoryNameMap[categoryName];
}

// 加载专辑详细数据
let eraData = { name: 'Unknown Album', tracks: [] };

try {
  const eraPath = path.join(process.cwd(), 'public', 'data', 'artists', 'ye', 'categories', categoryId, 'eras', `${eraId}.json`);
  const eraContent = await fs.readFile(eraPath, 'utf-8');
  eraData = JSON.parse(eraContent);
  
  if (eraData.tracks && eraData.tracks.length > 0) {
    eraData.tracks = eraData.tracks.map((track) => {
      // 处理originalContent字段，如果它是字符串，则尝试解析为JSON对象
      let processedTrack = { ...track };
      
      if (track.originalContent && typeof track.originalContent === 'string') {
        try {
          processedTrack.originalContent = JSON.parse(track.originalContent);
        } catch (e) {
          console.error(`Error parsing originalContent for track ${track.name}:`, e);
          // 如果解析失败，创建一个简单的对象来存储原始字符串
          processedTrack.originalContent = {
            rawContent: track.originalContent
          };
        }
      }
      
      return processedTrack;
    });
  }
} catch (error) {
  console.error(`Error loading era data for ${eraId}:`, error);
}

// 构建面包屑导航
const breadcrumbs = [
  { name: t('common.home', lang), url: '/pt' },
  { name: t('common.artists', lang), url: '/pt/artists' },
  { name: 'Ye', url: '/pt/artists/ye' },
  { name: t(`categories.${categoryId}`, lang) || categoryName, url: `/pt/artists/ye/${categoryId}` },
  { name: eraData.name, url: '#', current: true }
];

---

<MainLayout 
  title={`${eraData.name} - ${t(`categories.${categoryId}`, lang) || categoryName} - Ye - TrackerHive`}
  lang={lang}
>
  <style>
    /* Notes tooltip styles */
    .notes-tooltip {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s, visibility 0.2s;
    }
    
    .notes-tooltip:not(.hidden) {
      opacity: 1;
      visibility: visible;
    }
    
    .desktop-tooltip {
      pointer-events: none;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s, visibility 0.2s;
    }
    
    .notes-info-button:hover + .desktop-tooltip {
      opacity: 1;
      visibility: visible;
    }
    
    /* 自定义滚动条 */
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: rgba(59, 130, 246, 0.5);
      border-radius: 4px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: rgba(59, 130, 246, 0.7);
    }
    
    /* 音频播放器样式 */
    audio {
      width: 100%;
      height: 40px;
      outline: none;
    }
    
    audio::-webkit-media-controls-panel {
      background-color: rgba(31, 41, 55, 0.8);
    }
    
    audio::-webkit-media-controls-play-button,
    audio::-webkit-media-controls-mute-button {
      filter: invert(100%);
    }
    
    audio::-webkit-media-controls-current-time-display,
    audio::-webkit-media-controls-time-remaining-display {
      color: white;
    }
    
    audio::-webkit-media-controls-timeline {
      border: 1px solid rgba(59, 130, 246, 0.5);
    }
    
    .notes-info-button:hover + .notes-tooltip {
      opacity: 1;
      visibility: visible;
    }
  </style>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 ? (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4a1 1 0 011.414 1.414l4 4a1 1 0 01-1.414 1.414l-4 4a1 1 0 01-1.414-1.414l-4-4a1 1 0 010-1.414z"/>
              </svg>
            ) : null}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                index === breadcrumbs.length - 1 
                  ? 'text-text-primary cursor-default' 
                  : 'text-text-secondary hover:text-text-primary'
              }`}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    
    <!-- 专辑信息 -->
    <div class="bg-dark rounded-xl overflow-hidden shadow-md mb-8">
      <div class="md:flex">
        <div class="md:w-1/3 lg:w-1/4 p-6 flex justify-center items-center bg-black/20">
          {eraData.coverImage ? (
            <img 
              id="album-cover"
              src={eraData.coverImage} 
              alt={`${eraData.name} Cover`} 
              class="w-full max-w-[300px] h-auto rounded-lg shadow-lg"
            />
          ) : (
            <div class="w-full aspect-square max-w-[300px] bg-gray-800 rounded-lg flex items-center justify-center">
              <span class="text-text-secondary">{t('common.noCover', lang) || 'Sem Capa'}</span>
            </div>
          )}
        </div>
        <div class="md:w-2/3 lg:w-3/4 p-6">
          <h1 class="text-3xl font-bold text-text-primary mb-2">{eraData.name}</h1>
          
          {eraData.releaseDate && (
            <p class="text-text-secondary mb-4">
              <span class="font-semibold">{t('artist.releaseDate', lang) || 'Data de Lançamento'}:</span> {eraData.releaseDate}
            </p>
          )}
          
          {eraData.description && (
            <div class="mb-6">
              <h2 class="text-xl font-semibold text-text-primary mb-2">{t('artist.description', lang) || 'Descrição'}</h2>
              <p class="text-text-secondary">{eraData.description}</p>
            </div>
          )}
          
          {eraData.notes && (
            <div class="mb-6">
              <h2 class="text-xl font-semibold text-text-primary mb-2">{t('artist.notes', lang) || 'Notas'}</h2>
              <p class="text-text-secondary">{eraData.notes}</p>
            </div>
          )}
        </div>
      </div>
    </div>
    
    <!-- 曲目列表 -->
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-white mb-6">{t('track.trackList', lang) || 'Lista de Faixas'} ({eraData.tracks?.length || 0})</h2>
      
      <div class="bg-dark rounded-xl overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-800">
            <thead class="bg-black/20">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {t('track.track', lang) || 'Faixa'}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {t('track.length', lang) || 'Duração'}
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {t('track.quality', lang) || 'Qualidade/Disponível'}
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-800">
              {eraData.tracks?.map((track) => (
                <tr class="hover:bg-black/10 transition-colors">
                  <td class="px-6 py-4">
                    <div class="flex items-center">
                      {/* 根据是否有length显示不同的按钮 */}
                      <div class="mr-3 flex items-center">
                        {/* 桌面端支持notes悬浮展示，移动端不显示 */}
                        {track.length ? (
                          <button 
                            class="play-button w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white hover:bg-primary-dark transition-colors" 
                            title="Reproduzir"
                            data-track-name={track.name}
                            data-track-artists={track.artists?.join(', ')}
                            data-track-audio={track.audioUrl || ''}
                            data-track-original-url={track.originalUrl || ''}
                            data-track-original-content={JSON.stringify(track.originalContent || {})}
                            data-track-index={eraData.tracks.indexOf(track)}
                          >
                            {/* 播放图标 */}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 play-icon" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 11-16 0 8 8 0 0116 0zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                            </svg>
                            {/* 加载图标 */}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 loading-icon hidden animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M12 6v6l4 2"></path>
                            </svg>
                          </button>
                        ) : track.originalUrl ? (
                          <a href={track.originalUrl} target="_blank" rel="noopener noreferrer" class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors" title="Ir para a fonte">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                          </a>
                        ) : (
                          <div class="w-8 h-8"></div>
                        )}
                      </div>
                      <div class="track-content relative">
                        <div class="flex items-center gap-2">
                          <span class="text-white">{track.name}</span>
                          {/* 别名信息 */}
                          {track.aliases && track.aliases.length > 0 ? (
                            <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                              {Array.isArray(track.aliases) ? track.aliases.join(', ') : track.aliases}
                            </span>
                          ) : track.originalContent?.aliases && track.originalContent.aliases.length > 0 ? (
                            <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                              {Array.isArray(track.originalContent.aliases) ? track.originalContent.aliases.join(', ') : track.originalContent.aliases}
                            </span>
                          ) : null}
                        </div>
                        {/* 主要艺术家信息 */}
                        {track.artists && track.artists.length > 0 ? (
                          <p class="text-text-secondary text-sm mt-1">
                            {Array.isArray(track.artists) ? track.artists.join(', ') : track.artists}
                          </p>
                        ) : null}
                        
                        {/* 显示原始内容中的艺术家 - 作为字符串处理 */}
                        {track.originalContent && track.originalContent.artists && typeof track.originalContent.artists === 'string' && (
                          <p class="text-text-secondary text-sm mt-1">
                            {track.originalContent.artists}
                          </p>
                        )}
                        
                        {/* 如果originalContent.artists是数组，则使用join方法 */}
                        {track.originalContent && track.originalContent.artists && Array.isArray(track.originalContent.artists) && (
                          <p class="text-text-secondary text-sm mt-1">
                            {track.originalContent.artists.join(', ')}
                          </p>
                        )}
                        
                        {/* 添加notes信息，桌面端悬浮显示，移动端不显示 */}
                        {track.notes && (
                          <>
                            <button class="notes-info-button hidden md:block absolute top-0 right-0 opacity-0 pointer-events-none">
                              <span class="sr-only">Notas da Faixa</span>
                            </button>
                            <div class="hidden">{track.notes}</div>
                          </>
                        )}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <span class="track-length text-text-secondary" data-length={track.length}>
                      {track.length && /^\d+:\d{2}$/.test(track.length) ? track.length : ''}
                    </span>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex flex-wrap gap-1">
                      {/* 显示Quality和Available作为标签 */}
                      {track.quality ? (
                        <span class="quality-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                          {track.quality}
                        </span>
                      ) : null}
                      {track.availableLength ? (
                        <span class="available-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                          {track.availableLength}
                        </span>
                      ) : null}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </main>
</MainLayout>

<script>
  // 处理曲目时长，将数字转换为分:秒格式
  document.addEventListener('DOMContentLoaded', () => {
    // 初始化notes提示功能
    initNotesTooltips();
    
    // 初始化质量标签样式
    initQualityLabels();
    
    // 处理时长显示
    document.querySelectorAll('.track-length').forEach(span => {
      const length = span.getAttribute('data-length');
      if (length && /^\d+:\d{2}$/.test(length)) {
        span.textContent = length;
      }
    });
    
    // 获取所有播放按钮
    const playButtons = document.querySelectorAll('.play-button');
    
    // 准备曲目数据
    const tracks = [];
    playButtons.forEach((button) => {
      const trackName = button.getAttribute('data-track-name');
      const trackArtists = button.getAttribute('data-track-artists');
      const trackAudio = button.getAttribute('data-track-audio');
      
      // 只添加有音频URL的曲目
      if (trackAudio) {
        // 获取专辑名称
        const albumTitle = document.querySelector('.album-title')?.textContent || '';
        
        tracks.push({
          name: trackName,
          artists: trackArtists ? trackArtists.split(', ') : [],
          audioUrl: trackAudio,
          albumCover: document.querySelector('#album-cover')?.getAttribute('src') || '',
          album: albumTitle
        });
      }
    });
    
    // 当前正在播放的按钮
    let currentPlayingButton = null;
    
    // 重置所有按钮状态的函数
    function resetAllButtonsState() {
      playButtons.forEach(btn => {
        const btnPlayIcon = btn.querySelector('.play-icon');
        const btnLoadingIcon = btn.querySelector('.loading-icon');
        
        if (btnPlayIcon && btnLoadingIcon) {
          btnLoadingIcon.classList.add('hidden');
          btnPlayIcon.classList.remove('hidden');
        }
      });
    }
    
    // 为每个播放按钮添加点击事件
    playButtons.forEach((button) => {
      button.addEventListener('click', () => {
        // 重置所有按钮状态
        resetAllButtonsState();
        
        // 显示当前按钮的加载状态
        const playIcon = button.querySelector('.play-icon');
        const loadingIcon = button.querySelector('.loading-icon');
        
        if (playIcon && loadingIcon) {
          playIcon.classList.add('hidden');
          loadingIcon.classList.remove('hidden');
        }
        
        // 获取曲目信息
        const trackName = button.getAttribute('data-track-name');
        const trackArtists = button.getAttribute('data-track-artists');
        const audioUrl = button.getAttribute('data-track-audio');
        const originalUrl = button.getAttribute('data-track-original-url');
        const originalContentStr = button.getAttribute('data-track-original-content');
        const trackIndex = button.getAttribute('data-track-index');
        
        // 解析原始内容
        let originalContent = null;
        try {
          originalContent = JSON.parse(originalContentStr);
        } catch (e) {
          console.error('Failed to parse original content:', e);
        }
        
        // 如果有音频URL，播放音频
        if (audioUrl) {
          // 获取专辑名称
          const albumTitle = document.querySelector('.album-title')?.textContent || '';
          
          // 获取 originalContent 中的艺术家信息
          let artists = trackArtists ? trackArtists.split(', ') : [];
          if (originalContent && originalContent.artists) {
            artists = Array.isArray(originalContent.artists) 
              ? originalContent.artists 
              : [originalContent.artists];
          }
          
          // 触发自定义事件，通知播放器播放此曲目
          const playEvent = new CustomEvent('playTrack', {
            detail: {
              track: {
                name: trackName,
                artists: artists,
                audioUrl: audioUrl,
                albumCover: document.querySelector('#album-cover')?.getAttribute('src') || '',
                album: albumTitle,
                originalUrl: originalUrl || '',
                index: parseInt(trackIndex) || 0
              },
              button: button
            }
          });
          document.dispatchEvent(playEvent);
          
          // 记录当前播放的按钮
          currentPlayingButton = button;
        } else if (originalUrl) {
          // 如果没有音频但有原始链接，打开链接
          window.open(originalUrl, '_blank');
          
          // 重置按钮状态
          if (playIcon && loadingIcon) {
            loadingIcon.classList.add('hidden');
            playIcon.classList.remove('hidden');
          }
        }
      });
    });
  });
  
  // 初始化notes提示功能 - 桌面端悬浮展示，移动端不显示
  function initNotesTooltips() {
    // 为桌面端添加鼠标悬停效果
    const trackContents = document.querySelectorAll('.track-content');
    trackContents.forEach(content => {
      const infoButton = content.querySelector('.notes-info-button');
      if (infoButton) {
        const notesText = content.querySelector('.notes-info-button + div').textContent;
        
        // 创建tooltip元素
        const tooltip = document.createElement('div');
        tooltip.className = 'notes-tooltip';
        tooltip.textContent = notesText;
        content.appendChild(tooltip);
        
        // 鼠标悬停显示tooltip
        content.addEventListener('mouseenter', () => {
          tooltip.style.opacity = '1';
        });
        
        // 鼠标离开隐藏tooltip
        content.addEventListener('mouseleave', () => {
          tooltip.style.opacity = '0';
        });
      }
    });
  }
  
  // 初始化质量标签样式
  function initQualityLabels() {
    // 设置质量标签颜色
    document.querySelectorAll('.quality-label').forEach(label => {
      const quality = label.textContent.trim().toLowerCase();
      
      if (quality.includes('high') || quality.includes('alta')) {
        label.classList.add('bg-green-900', 'text-green-200');
      } else if (quality.includes('medium') || quality.includes('mid') || quality.includes('média')) {
        label.classList.add('bg-yellow-900', 'text-yellow-200');
      } else if (quality.includes('low') || quality.includes('baixa')) {
        label.classList.add('bg-red-900', 'text-red-200');
      } else if (quality.includes('lossless') || quality.includes('sem perdas')) {
        label.classList.add('bg-blue-900', 'text-blue-200');
      } else if (quality.includes('master') || quality.includes('mestre')) {
        label.classList.add('bg-purple-900', 'text-purple-200');
      } else {
        label.classList.add('bg-gray-700', 'text-gray-200');
      }
    });
    
    // 设置可用长度标签颜色
    document.querySelectorAll('.available-label').forEach(label => {
      label.classList.add('bg-indigo-900', 'text-indigo-200');
    });
  }
</script>
