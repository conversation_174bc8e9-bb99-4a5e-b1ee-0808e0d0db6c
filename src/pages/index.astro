---
import MainLayout from '../layouts/MainLayout.astro';
import artistsData from '../data/artists.json';
import seoData from '../data/seo.json';
import FeaturedTracks from '../components/FeaturedTracks.astro';
import SimpleSubmissionForm from '../components/content-submission/SimpleSubmissionForm.astro';

// Import i18n utilities
import { t, getLanguageFromURL } from '../i18n/index.js';

// Get language settings from URL
let currentLang = getLanguageFromURL(Astro.request.url);

// Check language parameter in URL
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}

// Set document direction
const dir = currentLang === 'ar' ? 'rtl' : 'ltr';

// Get featured artists
const featuredArtists = artistsData.artists.filter(artist => artist.featured);

// Get SEO data
const seo = seoData.home[currentLang];
---

<MainLayout 
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}>
  <!-- Main Content -->
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Hero Section -->
    <section class="mb-12">
      <div class="bg-gradient-to-r from-dark-elevated to-dark-secondary rounded-xl overflow-hidden shadow-lg">
        <div class="px-6 py-8 md:px-8 md:py-10 lg:py-14">
          <div class="md:max-w-2xl">
            <h1 class="text-2xl sm:text-3xl md:text-5xl font-bold mb-4 md:mb-6 text-white">{t('common.homeTitle', currentLang)}</h1>
            <p class="text-sm sm:text-base md:text-xl text-text-secondary mb-6 md:mb-8">{t('common.explore', currentLang, Astro.request.url)}</p>
            <div class="flex flex-wrap gap-3 md:gap-4">
              <a href="/artists/ye" class="inline-flex items-center justify-center px-5 sm:px-6 md:px-8 py-2.5 md:py-3 rounded-full bg-primary text-black font-medium hover:bg-primary-hover transition-colors shadow-md text-sm sm:text-base">
                {t('common.exploreArtist', currentLang, Astro.request.url)}
                <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
              <a href="/artists" class="inline-flex items-center justify-center px-5 sm:px-6 md:px-8 py-2.5 md:py-3 rounded-full bg-dark text-white font-medium border border-dark-hover hover:bg-dark-hover transition-colors text-sm sm:text-base">
                {t('common.browseAllArtists', currentLang, Astro.request.url)}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Featured Artists Section -->
    <section class="mb-16 sm:mb-20 lg:mb-24">
      <div class="flex items-center justify-between mb-6 md:mb-8">
        <h2 class="text-xl sm:text-2xl font-bold text-white">{t('common.topArtists', currentLang)}</h2>
        <a href="/artists" class="text-xs sm:text-sm text-primary hover:text-primary-hover font-medium flex items-center">
          {t('common.seeAll')}
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>

      <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
        {featuredArtists.map((artist) => (
          <a href={`/artists/${artist.id}`} class="group">
            <div class="bg-dark-elevated border border-dark-hover rounded-xl overflow-hidden transition-all hover:bg-dark-hover shadow-md hover:shadow-lg hover:border-primary/50">
              <div class="aspect-square bg-dark relative overflow-hidden">
                {/* Display artist photo or default icon */}
                {artist.image ? (
                  <img
                    src={artist.image}
                    alt={artist.name}
                    class="w-full h-full object-cover"
                    loading="lazy"
                    decoding="async"
                    fetchpriority="low"
                    onerror="this.onerror=null; this.src='/images/artists/placeholder.svg';"
                  />
                ) : (
                  <div class="absolute inset-0 flex items-center justify-center text-text-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 sm:h-16 sm:w-16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"/>
                      <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                      <circle cx="12" cy="10" r="3"/>
                    </svg>
                  </div>
                )}
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-70 group-hover:opacity-100 transition-opacity"></div>
                <div class="absolute bottom-0 left-0 right-0 p-3 sm:p-4">
                  <h3 class="font-semibold text-base sm:text-lg text-white">{artist.name}</h3>
                  <p class="text-xs sm:text-sm text-text-secondary">{artist.aliases ? artist.aliases[0] : ''}</p>
                </div>
              </div>
            </div>
          </a>
        ))}
      </div>
    </section>

    <!-- Latest Updates Section -->
    <div class="mb-16 sm:mb-20">
      <FeaturedTracks title={t('common.latestUpdates', currentLang)} />
    </div>

    <!-- Recently Played Section -->
    <section class="mb-16 sm:mb-20" id="recently-played-section">
      <div class="flex items-center justify-between mb-6 md:mb-8">
        <div>
          <h2 class="text-xl sm:text-2xl font-bold text-white">{t('common.recentlyPlayed', currentLang)}</h2>
          <p class="text-text-secondary text-xs sm:text-sm mt-2">{t('common.recentlyPlayedDesc', currentLang)}</p>
        </div>
        <button id="clear-history" class="text-xs sm:text-sm text-primary hover:text-primary-hover font-medium flex items-center">
          {t('common.clearHistory', currentLang)}
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
      
      <div id="no-recent-tracks" class="hidden bg-dark-elevated border border-dark-hover rounded-xl p-6 sm:p-8 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 sm:h-12 sm:w-12 mx-auto text-text-secondary mb-3 sm:mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
        </svg>
        <h3 class="text-base sm:text-lg font-medium text-white mb-2">{t('common.noRecentlyPlayed', currentLang)}</h3>
        <p class="text-text-secondary text-sm">{t('common.tracksWillAppear', currentLang)}</p>
      </div>
      
      <div id="recent-tracks-table" class="bg-dark rounded-xl overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="border-b border-dark-hover bg-dark-elevated">
              <tr>
                <th class="px-3 sm:px-5 py-3 sm:py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">#</th>
                <th class="px-3 sm:px-5 py-3 sm:py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">{t('common.track', currentLang)}</th>
                <th class="px-3 sm:px-5 py-3 sm:py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider hidden sm:table-cell">{t('common.album', currentLang)}</th>
                <th class="px-3 sm:px-5 py-3 sm:py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider hidden md:table-cell">{t('common.status.title', currentLang)}</th>
                <th class="px-3 sm:px-5 py-3 sm:py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider hidden lg:table-cell">{t('common.playedAt', currentLang)}</th>
                <th class="px-3 sm:px-5 py-3 sm:py-4 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">Download</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-dark-hover" id="recent-tracks-body">
              <!-- This will be dynamically populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- Add Track Section -->
    <div class="mb-20 sm:mb-24">
      <SimpleSubmissionForm />
    </div>

    <!-- Ye Tracker Features Section -->
    <section class="mt-16 sm:mt-20 mb-16 sm:mb-20">
      <div class="bg-dark-elevated rounded-xl p-6 sm:p-8 border border-dark-hover">
        <h2 class="text-xl sm:text-2xl font-bold text-white mb-6">Why Choose Our Yetracker Platform?</h2>
        <div class="grid md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div class="flex items-start space-x-3">
              <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <h3 class="font-semibold text-white mb-1">Comprehensive Ye Tracker Unreleased Database</h3>
                <p class="text-gray-400 text-sm">Access the largest collection of ye tracker unreleased music with our advanced yetracker system.</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <h3 class="font-semibold text-white mb-1">Exclusive Content Access</h3>
                <p class="text-gray-400 text-sm">Our kanye west tracker provides exclusive access to rare demos and unreleased tracks.</p>
              </div>
            </div>
          </div>
          <div class="space-y-4">
            <div class="flex items-start space-x-3">
              <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <h3 class="font-semibold text-white mb-1">Ye Tracker Art Collections</h3>
                <p class="text-gray-400 text-sm">Discover ye tracker art and visual content alongside our music database.</p>
              </div>
            </div>
            <div class="flex items-start space-x-3">
              <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <h3 class="font-semibold text-white mb-1">Free Yetracker Access</h3>
                <p class="text-gray-400 text-sm">Enjoy unlimited access to our yetracker platform with ye tracker unreleased content without any subscription fees.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section for SEO -->
    <section class="mt-20 sm:mt-24 mb-20 sm:mb-24 relative z-10">
      <div class="text-center mb-10 sm:mb-14">
        <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-6">
          Frequently Asked Questions About Our Yetracker
        </h2>
        <p class="text-gray-400 text-base sm:text-lg max-w-3xl mx-auto">
          Have questions about our yetracker platform? Learn more about our features and functionality below.
        </p>
      </div>

      <div class="max-w-4xl mx-auto space-y-4 sm:space-y-5">
        <!-- FAQ Item 1 -->
        <div class="faq-item bg-dark-elevated rounded-xl border border-dark-hover shadow-lg hover:shadow-xl transition-all duration-300">
          <button class="faq-question w-full px-5 sm:px-6 py-5 text-left flex justify-between items-center hover:bg-dark-hover/50 transition-colors rounded-xl" data-faq="1">
            <h3 class="text-base sm:text-lg font-semibold text-white pr-4 leading-tight">What is TrackerHive?</h3>
            <svg class="faq-icon w-5 h-5 text-primary transform transition-transform duration-300 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="faq-answer hidden px-5 sm:px-6 pb-5 pt-2">
            <div class="border-t border-dark-hover pt-4">
              <p class="text-gray-300 leading-relaxed text-sm sm:text-base">
                TrackerHive is a comprehensive platform for tracking unreleased and rare music from artists like yetracker and Playboi Carti.
              </p>
            </div>
          </div>
        </div>

        <!-- FAQ Item 2 -->
        <div class="faq-item bg-dark-elevated rounded-xl border border-dark-hover shadow-lg hover:shadow-xl transition-all duration-300">
          <button class="faq-question w-full px-5 sm:px-6 py-5 text-left flex justify-between items-center hover:bg-dark-hover/50 transition-colors rounded-xl" data-faq="2">
            <h3 class="text-base sm:text-lg font-semibold text-white pr-4 leading-tight">How can I download tracks?</h3>
            <svg class="faq-icon w-5 h-5 text-primary transform transition-transform duration-300 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="faq-answer hidden px-5 sm:px-6 pb-5 pt-2">
            <div class="border-t border-dark-hover pt-4">
              <p class="text-gray-300 leading-relaxed text-sm sm:text-base">
                Click the download button next to any track. Our ye tracker unreleased database provides high-quality audio files when available.
              </p>
            </div>
          </div>
        </div>

        <!-- FAQ Item 3 -->
        <div class="faq-item bg-dark-elevated rounded-xl border border-dark-hover shadow-lg hover:shadow-xl transition-all duration-300">
          <button class="faq-question w-full px-5 sm:px-6 py-5 text-left flex justify-between items-center hover:bg-dark-hover/50 transition-colors rounded-xl" data-faq="3">
            <h3 class="text-base sm:text-lg font-semibold text-white pr-4 leading-tight">Are these tracks officially released?</h3>
            <svg class="faq-icon w-5 h-5 text-primary transform transition-transform duration-300 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="faq-answer hidden px-5 sm:px-6 pb-5 pt-2">
            <div class="border-t border-dark-hover pt-4">
              <p class="text-gray-300 leading-relaxed text-sm sm:text-base">
                Most tracks are ye tracker unreleased, leaked, or rare versions not available on streaming platforms.
              </p>
            </div>
          </div>
        </div>

        <!-- FAQ Item 4 -->
        <div class="faq-item bg-dark-elevated rounded-xl border border-dark-hover shadow-lg hover:shadow-xl transition-all duration-300">
          <button class="faq-question w-full px-5 sm:px-6 py-5 text-left flex justify-between items-center hover:bg-dark-hover/50 transition-colors rounded-xl" data-faq="4">
            <h3 class="text-base sm:text-lg font-semibold text-white pr-4 leading-tight">How often is the database updated?</h3>
            <svg class="faq-icon w-5 h-5 text-primary transform transition-transform duration-300 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="faq-answer hidden px-5 sm:px-6 pb-5 pt-2">
            <div class="border-t border-dark-hover pt-4">
              <p class="text-gray-300 leading-relaxed text-sm sm:text-base">
                We continuously update our ye tracker unreleased collection with new leaks and discoveries. Our ye tracker unreleased database grows daily.
              </p>
            </div>
          </div>
        </div>

        <!-- FAQ Item 5 -->
        <div class="faq-item bg-dark-elevated rounded-xl border border-dark-hover shadow-lg hover:shadow-xl transition-all duration-300">
          <button class="faq-question w-full px-5 sm:px-6 py-5 text-left flex justify-between items-center hover:bg-dark-hover/50 transition-colors rounded-xl" data-faq="5">
            <h3 class="text-base sm:text-lg font-semibold text-white pr-4 leading-tight">Is TrackerHive free to use?</h3>
            <svg class="faq-icon w-5 h-5 text-primary transform transition-transform duration-300 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="faq-answer hidden px-5 sm:px-6 pb-5 pt-2">
            <div class="border-t border-dark-hover pt-4">
              <p class="text-gray-300 leading-relaxed text-sm sm:text-base">
                Yes, TrackerHive is completely free for all users. You can access all content including ye tracker unreleased music without any subscription fees.
              </p>
            </div>
          </div>
        </div>


      </div>
    </section>
  </main>
</MainLayout>

<script>
  // 获取当前语言
  const currentLang = document.documentElement.lang || 'en';
  
  // 翻译数据对象 - 从服务器端获取的翻译数据
  const translations = {
    en: {
      status: {
        title: "Status",
        official: "Official",
        leaked: "Leaked",
        unreleased: "Unreleased",
        unknown: "Unknown"
      },
      played: "played",
      unknown: "Unknown",
      timeAgo: {
        seconds: "seconds ago",
        minutes: "minutes ago",
        hours: "hours ago",
        days: "days ago"
      }
    },
    ar: {
      status: {
        title: "الحالة",
        official: "رسمي",
        leaked: "مخترق",
        unreleased: "غير منشور",
        unknown: "غير معروف"
      },
      played: "تم تشغيله",
      unknown: "غير معروف",
      timeAgo: {
        seconds: "ثانية مضت",
        minutes: "دقيقة مضت",
        hours: "ساعة مضت",
        days: "يوم مضى"
      }
    },
    pt: {
      status: {
        title: "Status",
        official: "Oficial",
        leaked: "Vazado",
        unreleased: "Não Lançado",
        unknown: "Desconhecido"
      },
      played: "reproduzido",
      unknown: "Desconhecido",
      timeAgo: {
        seconds: "segundos atrás",
        minutes: "minutos atrás",
        hours: "horas atrás",
        days: "dias atrás"
      }
    }
  };
  
  // 简单的翻译函数
  function t(key, lang) {
    // 支持嵌套键，如 'common.status.official'
    const keys = key.split('.');
    let result = translations[lang] || translations['en'];
    
    for (const k of keys) {
      if (result && result[k] !== undefined) {
        result = result[k];
      } else {
        // 如果找不到翻译，回退到英语
        return key.split('.').pop();
      }
    }
    
    return result;
  }
  
  // 检查播放历史
  function checkPlayHistory() {
    try {
      const historyJson = localStorage.getItem('playHistory');
      return historyJson ? JSON.parse(historyJson) : [];
    } catch (error) {
      return [];
    }
  }
  
  // 更新最近播放列表 - 简化版
  function updateRecentlyPlayed() {
    // 直接从localStorage获取播放历史
    const playHistory = checkPlayHistory();
    
    // 获取表格主体和相关元素
    const tableBody = document.getElementById('recent-tracks-body');
    const noTracksEl = document.getElementById('no-recent-tracks');
    const tableEl = document.getElementById('recent-tracks-table');
    
    if (!tableBody || !noTracksEl || !tableEl) {
      return;
    }
    
    // 如果没有最近播放记录，显示空状态
    if (!playHistory || playHistory.length === 0) {
      noTracksEl.classList.remove('hidden');
      tableEl.classList.add('hidden');
      return;
    }
    
    // 否则显示表格
    noTracksEl.classList.add('hidden');
    tableEl.classList.remove('hidden');
    
    // 清空表格
    tableBody.innerHTML = '';
    
    // 添加最近播放记录
    playHistory.forEach((track, index) => {
      const row = document.createElement('tr');
      row.className = 'hover:bg-dark-hover transition-colors';
      
      // 创建单元格
      const indexCell = document.createElement('td');
      indexCell.className = 'px-3 sm:px-5 py-3 sm:py-4 whitespace-nowrap text-sm text-text-secondary';
      indexCell.textContent = index + 1;
      
      const titleCell = document.createElement('td');
      titleCell.className = 'px-3 sm:px-5 py-3 sm:py-4 whitespace-nowrap';
      
      const titleLink = document.createElement('a');
      titleLink.href = 'javascript:void(0)';
      titleLink.className = 'text-white hover:text-primary text-sm sm:text-base font-medium flex items-center cursor-pointer';
      titleLink.dataset.trackId = track.id || '';
      
      // 添加播放图标
      const playIcon = document.createElement('span');
      playIcon.className = 'mr-2 text-primary';
      playIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" /></svg>';
      
      titleLink.appendChild(playIcon);
      
      const titleText = document.createTextNode(track.title || track.name || 'Unknown Track');
      titleLink.appendChild(titleText);
      
      // 添加点击事件直接播放
      titleLink.addEventListener('click', playTrackHandler);
      titleLink.addEventListener('touchend', function(e) {
        e.preventDefault(); // 防止触发点击事件
        playTrackHandler.call(this, e);
      });
      
      function playTrackHandler() {
        if (window.playTrack && typeof window.playTrack === 'function') {
          window.playTrack(track);
        }
      }
      
      const artistSpan = document.createElement('div');
      artistSpan.className = 'text-text-secondary text-xs sm:text-sm';
      artistSpan.textContent = track.artist || 'Unknown Artist';
      
      titleCell.appendChild(titleLink);
      titleCell.appendChild(artistSpan);
      
      const albumCell = document.createElement('td');
      albumCell.className = 'px-3 sm:px-5 py-3 sm:py-4 whitespace-nowrap text-sm text-text-secondary hidden sm:table-cell';
      albumCell.textContent = track.album || 'Unknown Album';
      
      const statusCell = document.createElement('td');
      statusCell.className = 'px-3 sm:px-5 py-3 sm:py-4 whitespace-nowrap hidden md:table-cell';
      
      const statusBadge = document.createElement('span');
      statusBadge.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        track.status === 'Official' ? 'bg-green-900 text-green-200' :
        track.status === 'Leaked' ? 'bg-red-900 text-red-200' :
        track.status === 'Unreleased' ? 'bg-yellow-900 text-yellow-200' :
        'bg-gray-700 text-gray-200'
      }`;
      
      // 使用翻译函数获取状态文本
      statusBadge.textContent = track.status ? t(`status.${track.status.toLowerCase()}`, currentLang) : t('status.unknown', currentLang);
      
      statusCell.appendChild(statusBadge);
      
      const dateCell = document.createElement('td');
      dateCell.className = 'px-3 sm:px-5 py-3 sm:py-4 whitespace-nowrap text-sm text-text-secondary hidden lg:table-cell';
      
      if (track.playedAt) {
        try {
          const date = new Date(track.playedAt);
          const now = new Date();
          const diffMs = now - date;
          
          // 计算相对时间
          let timeAgo;
          const seconds = Math.floor(diffMs / 1000);
          const minutes = Math.floor(seconds / 60);
          const hours = Math.floor(minutes / 60);
          const days = Math.floor(hours / 24);
          
          if (seconds < 60) {
            timeAgo = seconds + ' ' + t('timeAgo.seconds', currentLang);
          } else if (minutes < 60) {
            timeAgo = minutes + ' ' + t('timeAgo.minutes', currentLang);
          } else if (hours < 24) {
            timeAgo = hours + ' ' + t('timeAgo.hours', currentLang);
          } else {
            timeAgo = days + ' ' + t('timeAgo.days', currentLang);
          }
          
          // 添加"播放于"文本
          dateCell.textContent = timeAgo;
        } catch (e) {
          dateCell.textContent = t('unknown', currentLang);
        }
      } else {
        dateCell.textContent = t('unknown', currentLang);
      }

      // 下载列
      const downloadCell = document.createElement('td');
      downloadCell.className = 'px-3 sm:px-5 py-3 sm:py-4 text-center';



      if (track.url || track.audioUrl) {
        const downloadContainer = document.createElement('div');
        downloadContainer.className = 'download-button-container track-download flex justify-center';
        downloadContainer.setAttribute('data-track-id', track.id || `recent_${track.name}`);
        downloadContainer.setAttribute('data-track-title', track.title || track.name || '');
        downloadContainer.setAttribute('data-download-url', track.url || track.audioUrl || '');
        downloadContainer.setAttribute('data-track-audio', track.url || track.audioUrl || '');
        downloadContainer.setAttribute('data-track-original-url', track.originalUrl || '');
        downloadContainer.setAttribute('data-track-original-content', JSON.stringify(track.originalContent || {}));

        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'download-btn bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-gray-100 hover:text-white rounded-lg w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-gray-500/40 hover:border-gray-400/60';
        downloadBtn.setAttribute('data-action', 'download');
        downloadBtn.setAttribute('title', 'Download track');
        downloadBtn.innerHTML = `
          <svg class="download-icon w-4 h-4 sm:w-4.5 sm:h-4.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        `;

        downloadContainer.appendChild(downloadBtn);
        downloadCell.appendChild(downloadContainer);
      } else {
        const noDownload = document.createElement('span');
        noDownload.className = 'text-gray-500 text-sm';
        noDownload.textContent = '-';
        downloadCell.appendChild(noDownload);
      }

      // 添加所有单元格到行
      row.appendChild(indexCell);
      row.appendChild(titleCell);
      row.appendChild(albumCell);
      row.appendChild(statusCell);
      row.appendChild(dateCell);
      row.appendChild(downloadCell);
      
      // 添加行到表格
      tableBody.appendChild(row);
    });
  }
  
  document.addEventListener('DOMContentLoaded', () => {
    // 立即检查并显示播放历史
    updateRecentlyPlayed();
    
    // 添加清除历史记录按钮事件
    const clearButton = document.getElementById('clear-history');
    if (clearButton) {
      clearButton.addEventListener('click', clearHistoryHandler);
      clearButton.addEventListener('touchend', function(e) {
        e.preventDefault(); // 防止触发点击事件
        clearHistoryHandler();
      });
    }
    
    // 监听播放历史更新事件
    document.addEventListener('play-history-updated', () => {
      updateRecentlyPlayed();
    });
    
    // 监听音频播放器动作事件
    document.addEventListener('audio-player-action', (event) => {
      if (event.detail && event.detail.action === 'play') {
        setTimeout(updateRecentlyPlayed, 300);
      }
    });
    
    // 确保在移动端也能正确显示
    if (window.matchMedia) {
      // 监听媒体查询变化
      window.matchMedia('(max-width: 1024px)').addEventListener('change', () => {
        updateRecentlyPlayed();
      });
    }
  });
  
  // 清除历史记录处理函数
  function clearHistoryHandler() {
    localStorage.removeItem('playHistory');
    updateRecentlyPlayed();
  }

  // FAQ功能 - 优化版本
  document.addEventListener('DOMContentLoaded', () => {
    const faqQuestions = document.querySelectorAll('.faq-question');

    faqQuestions.forEach(question => {
      question.addEventListener('click', () => {
        const faqItem = question.closest('.faq-item');
        const answer = faqItem.querySelector('.faq-answer');
        const icon = question.querySelector('.faq-icon');

        // 关闭其他打开的FAQ项目（可选：实现手风琴效果）
        // faqQuestions.forEach(otherQuestion => {
        //   if (otherQuestion !== question) {
        //     const otherItem = otherQuestion.closest('.faq-item');
        //     const otherAnswer = otherItem.querySelector('.faq-answer');
        //     const otherIcon = otherQuestion.querySelector('.faq-icon');
        //     otherAnswer.classList.add('hidden');
        //     otherIcon.style.transform = 'rotate(0deg)';
        //   }
        // });

        // 切换当前答案显示
        const isHidden = answer.classList.contains('hidden');
        answer.classList.toggle('hidden');

        // 平滑旋转图标
        if (isHidden) {
          icon.style.transform = 'rotate(180deg)';
        } else {
          icon.style.transform = 'rotate(0deg)';
        }

        // 添加视觉反馈
        faqItem.classList.toggle('ring-2');
        faqItem.classList.toggle('ring-primary/20');
      });
    });
  });

</script>

<!-- FAQ Schema Markup for SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "What exactly is TrackerHive and how does yetracker work?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "TrackerHive is a comprehensive yetracker platform specifically designed for tracking unreleased and rare music from artists like Kanye West and Playboi Carti. Our ye tracker unreleased system provides ready-to-use access to leaked tracks, demos, and rare versions that help you discover hidden gems in hours instead of days. The kanye west tracker functionality makes it easy to explore Ye's extensive unreleased catalog with detailed metadata and high-quality audio files."
      }
    },
    {
      "@type": "Question",
      "name": "How can I download tracks using the ye tracker system?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Simply click the download button next to any track in our yetracker database. Our kanye tracker and carti tracker systems provide high-quality audio files when available. The ye tracker unreleased collection includes detailed information about each track's quality, duration, and source. All downloads through our kanye west tracker are free and don't require registration, making it the most user-friendly ye tracker experience available."
      }
    },
    {
      "@type": "Question",
      "name": "Are these tracks in the kanye west tracker officially released?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Most tracks in our yetracker database are unreleased, leaked, or rare versions not available on streaming platforms. Our ye tracker unreleased section specifically focuses on demos, alternate versions, and scrapped songs from various eras. The kanye tracker includes everything from early College Dropout demos to recent Donda sessions. Our carti tracker similarly covers unreleased Playboi Carti material, making this the most comprehensive ye tracker for discovering hidden music."
      }
    },
    {
      "@type": "Question",
      "name": "How often is the yetracker database updated?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We continuously update our ye tracker unreleased collection with new leaks and discoveries from the community. Our kanye west tracker receives daily updates when new material surfaces, and the carti tracker is similarly maintained with fresh content. The yetracker system automatically indexes new releases, ensuring you always have access to the latest finds. Our dedicated team monitors various sources to keep the ye tracker database current and comprehensive."
      }
    },
    {
      "@type": "Question",
      "name": "Is the kanye tracker completely free to use?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, TrackerHive's yetracker platform is completely free for all users. Our ye tracker unreleased database, kanye west tracker features, and carti tracker functionality are all available without any subscription fees or hidden costs. We believe in providing free access to rare music discoveries, making our ye tracker the most accessible platform for exploring unreleased content from your favorite artists."
      }
    },
    {
      "@type": "Question",
      "name": "What future features will enhance the ye tracker experience?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We're constantly improving our yetracker platform with exciting new features. Upcoming enhancements to the ye tracker unreleased system include advanced search filters, personalized playlists, and community voting on track authenticity. The kanye west tracker will soon feature AI-powered recommendations and enhanced metadata. Our carti tracker will expand to include more artists, while the core ye tracker functionality will add real-time notifications for new leaks and improved mobile experience."
      }
    }
  ]
}
</script>
