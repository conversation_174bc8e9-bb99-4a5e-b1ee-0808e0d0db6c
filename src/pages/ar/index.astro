---
// Arabic homepage
import MainLayout from '../../layouts/MainLayout.astro';
import artistsData from '../../data/artists.json';
import seoData from '../../data/seo.json';
import FeaturedTracks from '../../components/FeaturedTracks.astro';
import { t } from '../../i18n';

// Set Arabic language environment
const lang = 'ar';

// Set document direction for RTL
const dir = 'rtl';

// Get featured artists
const featuredArtists = artistsData.artists.filter(artist => artist.featured);

// Get SEO data
const seo = seoData.home[lang];
---

<MainLayout 
  title={seo.title}
  description={seo.description}
  keywords={seo.keywords}>
  <!-- Main Content -->
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Hero Section -->
    <section class="mb-12">
      <div class="bg-gradient-to-r from-dark-elevated to-dark-secondary rounded-xl overflow-hidden shadow-lg">
        <div class="px-8 py-10 md:py-14">
          <div class="md:max-w-2xl">
            <h1 class="text-3xl md:text-5xl font-bold mb-6 text-white">{t('common.homeTitle', lang)}</h1>
            <p class="text-base md:text-xl text-text-secondary mb-8">{t('common.explore', lang)}</p>
            <div class="flex flex-wrap gap-4">
              <a href="/ar/artists/ye" class="inline-flex items-center justify-center px-8 py-3 rounded-full bg-primary text-black font-medium hover:bg-primary-hover transition-colors shadow-md">
                {t('common.exploreArtist', lang)}
                <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
              <a href="/ar/artists" class="inline-flex items-center justify-center px-8 py-3 rounded-full bg-dark text-white font-medium border border-dark-hover hover:bg-dark-hover transition-colors">
                {t('common.browseAllArtists', lang)}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Featured Artists Section -->
    <section class="mb-16">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-white">{t('common.topArtists', lang)}</h2>
        <a href="/ar/artists" class="text-sm text-primary hover:text-primary-hover font-medium flex items-center">
          {t('common.seeAll', lang)}
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-5 md:gap-8">
        {featuredArtists.map((artist) => (
          <a href={`/ar/artists/${artist.id}`} class="group">
            <div class="bg-dark-elevated border border-dark-hover rounded-xl overflow-hidden transition-all hover:bg-dark-hover shadow-md hover:shadow-lg hover:border-primary/50">
              <div class="aspect-square bg-dark relative overflow-hidden">
                {artist.image ? (
                  <img 
                    src={artist.image} 
                    alt={artist.name} 
                    class="w-full h-full object-cover" 
                    onerror="this.onerror=null; this.src='/images/artists/placeholder.svg';"
                  />
                ) : (
                  <div class="absolute inset-0 flex items-center justify-center text-text-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"/>
                      <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                      <circle cx="12" cy="10" r="3"/>
                    </svg>
                  </div>
                )}
              </div>
              <div class="p-4">
                <h3 class="font-semibold text-lg text-white">{artist.name}</h3>
                <p class="text-sm text-text-secondary">{artist.aliases ? artist.aliases[0] : ''}</p>
              </div>
            </div>
          </a>
        ))}
      </div>
    </section>
    
    <!-- Latest Updates Section -->
    <FeaturedTracks title={t('common.latestUpdates', lang)} />

    <!-- Recently Played Section -->
    <section class="mb-8" id="recently-played-section">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-2xl font-bold text-white">{t('common.recentlyPlayed', lang)}</h2>
          <p class="text-text-secondary text-sm mt-1">{t('common.recentlyPlayedDesc', lang)}</p>
        </div>
        <button id="clear-history" class="text-sm text-primary hover:text-primary-hover font-medium flex items-center">
          {t('common.clearHistory', lang)}
          <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
      
      <div id="no-recent-tracks" class="hidden bg-dark-elevated border border-dark-hover rounded-xl p-8 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-text-secondary mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
        </svg>
        <h3 class="text-lg font-medium text-white mb-2">{t('common.noRecentlyPlayed', lang)}</h3>
        <p class="text-text-secondary">{t('common.tracksWillAppear', lang)}</p>
      </div>
      
      <div id="recent-tracks-table" class="bg-dark rounded-xl overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="border-b border-dark-hover bg-dark-elevated">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-text-secondary tracking-wider">#</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-text-secondary tracking-wider">{t('common.track', lang)}</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-text-secondary tracking-wider">{t('common.album', lang)}</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-text-secondary tracking-wider">{t('common.status', lang)}</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-text-secondary tracking-wider">{t('common.playedAt', lang)}</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-dark-hover" id="recent-tracks-body">
              <!-- This will be dynamically populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </main>
</MainLayout>

<script>
  // 翻译文本
  const translations = {
    status: {
      official: 'رسمي',
      leaked: 'مخترق',
      unreleased: 'غير منشور',
      unknown: 'غير معروف'
    },
    unknownTrack: 'مسار غير معروف',
    confirmClearHistory: 'هل أنت متأكد من أنك تريد مسح سجل التشغيل؟'
  };

  // 翻译函数
  function t(key, obj = translations) {
    const keys = key.split('.');
    let result = obj;

    for (const k of keys) {
      if (result && result[k] !== undefined) {
        result = result[k];
      } else {
        return key.split('.').pop();
      }
    }

    return result;
  }

  function formatRelativeTime(timestamp) {
    const rtf = new Intl.RelativeTimeFormat('ar', { numeric: 'auto' });
    const now = new Date();
    const then = new Date(timestamp);
    const diffInSeconds = Math.floor((then - now) / 1000);

    if (Math.abs(diffInSeconds) < 60) return rtf.format(Math.floor(diffInSeconds), 'second');
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (Math.abs(diffInMinutes) < 60) return rtf.format(diffInMinutes, 'minute');
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (Math.abs(diffInHours) < 24) return rtf.format(diffInHours, 'hour');
    const diffInDays = Math.floor(diffInHours / 24);
    if (Math.abs(diffInDays) < 30) return rtf.format(diffInDays, 'day');
    const diffInMonths = Math.floor(diffInDays / 30);
    if (Math.abs(diffInMonths) < 12) return rtf.format(diffInMonths, 'month');
    const diffInYears = Math.floor(diffInMonths / 12);
    return rtf.format(diffInYears, 'year');
  }
  
  function updateRecentlyPlayed() {
    const tableBody = document.getElementById('recent-tracks-body');
    const playHistory = JSON.parse(localStorage.getItem('playHistory') || '[]');
    
    const noTracksEl = document.getElementById('no-recent-tracks');
    const tableEl = document.getElementById('recent-tracks-table');
    
    if (!playHistory || playHistory.length === 0) {
      noTracksEl.classList.remove('hidden');
      tableEl.classList.add('hidden');
      return;
    }
    
    noTracksEl.classList.add('hidden');
    tableEl.classList.remove('hidden');
    
    tableBody.innerHTML = '';
    
    playHistory.forEach((track, index) => {
      const row = document.createElement('tr');
      row.className = 'hover:bg-dark-hover transition-colors group';
      row.setAttribute('data-track', JSON.stringify(track));
      
      row.addEventListener('click', function() {
        const trackData = JSON.parse(this.getAttribute('data-track'));
        const playEvent = new CustomEvent('play-track', {
          detail: {
            track: trackData,
            playlist: [trackData],
            index: 0
          }
        });
        document.dispatchEvent(playEvent);
      });
      
      // 生成状态标签
      let statusHtml = '';
      const statusClass = track.status === 'Official' ? 'bg-green-900 text-green-200' :
                         track.status === 'Leaked' ? 'bg-red-900 text-red-200' :
                         track.status === 'Unreleased' ? 'bg-yellow-900 text-yellow-200' :
                         'bg-gray-700 text-gray-200';

      const statusText = track.status ? t(`status.${track.status.toLowerCase()}`) : t('status.unknown');
      statusHtml = `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">${statusText}</span>`;
      
      row.innerHTML = `
        <td class="px-4 py-3 whitespace-nowrap">
          <div class="flex items-center">
            <span class="text-text-secondary group-hover:hidden">${index + 1}</span>
            <span class="text-white hidden group-hover:block cursor-pointer">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
            </span>
          </div>
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-dark-secondary rounded flex-shrink-0 ml-3 overflow-hidden">
              <img src="${track.albumCover || '/images/album-placeholder.svg'}" alt="${track.name}" class="w-full h-full object-cover">
            </div>
            <div>
              <div class="text-sm font-medium text-white cursor-pointer hover:text-primary">
                ${track.name || translations.unknownTrack}
              </div>
              <div class="text-sm text-text-secondary">${track.artists ? (Array.isArray(track.artists) ? track.artists.join(', ') : track.artists) : '-'}</div>
            </div>
          </div>
        </td>
        <td class="px-4 py-3 whitespace-nowrap text-sm text-text-secondary">
          ${track.album || '-'}
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
          ${statusHtml || '-'}
        </td>
        <td class="px-4 py-3 whitespace-nowrap text-sm text-text-secondary">
          ${formatRelativeTime(track.playedAt)}
        </td>
      `;
      
      tableBody.appendChild(row);
    });
  }
  
  document.addEventListener('DOMContentLoaded', () => {
    updateRecentlyPlayed();
    
    const clearButton = document.getElementById('clear-history');
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        if (confirm(translations.confirmClearHistory)) {
          localStorage.removeItem('playHistory');
          updateRecentlyPlayed();
          
          const event = new CustomEvent('play-history-updated', {
            detail: { history: [] }
          });
          document.dispatchEvent(event);
        }
      });
    }
    
    document.addEventListener('play-history-updated', () => {
      updateRecentlyPlayed();
    });
  });
</script>
