---
import MainLayout from '../../../layouts/MainLayout.astro';
import { promises as fs } from 'fs';
import path from 'path';
import SmartImage from '../../../components/SmartImage.astro';
import { t } from '../../../i18n/index.js';

import AlbumGridDisplay from '../../../components/categoryViews/AlbumGridDisplay.astro';
import TrackListDisplay from '../../../components/categoryViews/TrackListDisplay.astro';
import ArtworkDisplay from '../../../components/categoryViews/ArtworkDisplay.astro';
import CollapsibleSubmissionForm from '../../../components/content-submission/CollapsibleSubmissionForm.astro';
import type { Track } from '../../../types/Track';
import type { Artwork } from '../../../types/Artwork';

// 获取所有分类用于静态生成
export const prerender = true;

export async function getStaticPaths() {
  // 在函数内部定义分类列表，确保与 playboi-carti.astro 中的列表一致
  const cartiCategories = [
    { id: 'art', name: 'Art' },
    { id: 'best-of', name: 'Best Of' },
    { id: 'fakes', name: 'Fakes' },
    { id: 'grails', name: 'Grails' },
    { id: 'groupbuys', name: 'Groupbuys' },
    { id: 'leaks', name: 'Leaks' },
    { id: 'misc', name: 'Misc' },
    { id: 'recent', name: 'Recent' },
    { id: 'released', name: 'Released' },
    { id: 'snippets', name: 'Snippets' },
    { id: 'special', name: 'Special' },
    { id: 'stems', name: 'Stems' },
    { id: 'tracklists', name: 'Tracklists' },
    { id: 'unreleased', name: 'Unreleased' },
    { id: 'worst-of', name: 'Worst Of' }
  ];
  
  const paths = [];

  for (const category of cartiCategories) {
    paths.push({
      params: { categoryId: category.id },
      props: { categoryName: category.name }
    });
  }

  return paths;
}

// 获取特定分类数据
const { categoryId } = Astro.params;
const { categoryName } = Astro.props;
const artistId = 'playboi-carti';

// 定义分类列表（确保与 playboi-carti.astro 和 getStaticPaths 中的列表一致）
const allCategories = [
  { id: 'art', name: 'Art' },
  { id: 'best-of', name: 'Best Of' },
  { id: 'fakes', name: 'Fakes' },
  { id: 'grails', name: 'Grails' },
  { id: 'groupbuys', name: 'Groupbuys' },
  { id: 'leaks', name: 'Leaks' },
  { id: 'misc', name: 'Misc' },
  { id: 'recent', name: 'Recent' },
  { id: 'released', name: 'Released' },
  { id: 'snippets', name: 'Snippets' },
  { id: 'special', name: 'Special' },
  { id: 'stems', name: 'Stems' },
  { id: 'tracklists', name: 'Tracklists' },
  { id: 'unreleased', name: 'Unreleased' },
  { id: 'worst-of', name: 'Worst Of' }
];

// 设置语言为英文
const lang = 'en';

// 面包屑导航数据 - 修正顺序
const breadcrumbs = [
  { name: t('navigation.home', lang) || 'Home', url: '/' },
  { name: t('common.artists', lang) || 'Artists', url: '/artists' },
  { name: 'Playboi Carti', url: '/artists/playboi-carti' },
  { name: categoryName, url: `/artists/playboi-carti/${categoryId}`, current: true }
];

// 构建优化的 title 和 description
const pageTitle = `${categoryName} - Playboi Carti - TrackerHive - ${t('common.slogan', lang) || 'Digital Music Library'}`;
const pageDescription = `Explore ${categoryName} by Playboi Carti - ${t(`categories.${categoryId}.description`, lang) || 'A complete collection of music, art, and exclusive content.'} | ${t('common.siteDescription', lang) || 'The largest library of unreleased music and exclusive content'}`;

// 构建 canonical URL
const canonicalUrl = Astro.site ? new URL(`/artists/playboi-carti/${categoryId}`, Astro.site).toString() : `/artists/playboi-carti/${categoryId}`;

// 加载分类信息和数据
let categoryInfo = null;
let albums = [];
let tracks: Track[] = [];
let artworks: Artwork[] = [];

try {
  // 尝试加载分类信息
  const categoryInfoPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'info.json');
  
  try {
    const categoryInfoData = await fs.readFile(categoryInfoPath, 'utf-8');
    categoryInfo = JSON.parse(categoryInfoData);
  } catch (infoError) {
    console.error(`未找到分类信息: ${categoryId}:`, infoError);
  }

  // 从info.json中获取显示模式，默认为'albums'
  const displayMode = categoryInfo?.displayMode || 'albums';

  // 只在albums模式下加载eras.json
  if (displayMode === 'albums') {
    try {
      const categoryErasPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');
      const erasData = await fs.readFile(categoryErasPath, 'utf-8');
      albums = JSON.parse(erasData);
      
      // 确保每个专辑都有coverImage和backgroundColor字段，并检查是否有数据
      albums = await Promise.all(albums.map(async (album) => {
        // 检查该 era 是否有实际的数据文件
        let hasData = false;
        try {
          const eraDataPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras', `${album.id}.json`);
          const eraData = await fs.readFile(eraDataPath, 'utf-8');
          const parsedEraData = JSON.parse(eraData);
          hasData = parsedEraData && parsedEraData.length > 0;
        } catch (error) {
          hasData = false;
        }

        return {
          ...album,
          coverImage: album.coverImage || '/images/eras/default.svg',
          backgroundColor: album.backgroundColor || '#333',
          hasData
        };
      }));
    } catch (erasError) {
      console.error(`未找到eras数据: ${categoryId}:`, erasError);
    }
  }
} catch (error) {
  console.error(`加载分类数据时出错: ${categoryId}:`, error);
}

// 从info.json中获取显示模式，默认为'albums'
const displayMode = categoryInfo?.displayMode || 'albums';

// 尝试加载tracks或artwork数据
try {
  const tracksPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'tracks.json');
  const tracksData = await fs.readFile(tracksPath, 'utf-8');
  const parsedData = JSON.parse(tracksData);
  
  if (displayMode === 'flat') {
    tracks = parsedData;
  } else if (displayMode === 'poster') {
    artworks = parsedData;
  }
} catch (tracksError) {
  // 只在flat或poster模式下记录错误
  if (displayMode === 'flat' || displayMode === 'poster') {
    console.log(`注意: 未找到tracks.json: ${categoryId}, 但这只在flat/poster模式下需要。`);
  }
}

// 检查分类是否有数据
const hasData = ['art', 'best-of', 'recent', 'unreleased'].includes(categoryId) && 
  ((displayMode === 'albums' && albums.length > 0) || 
   (displayMode === 'flat' && tracks.length > 0) || 
   (displayMode === 'poster' && artworks.length > 0));

// 根据displayMode确定要渲染的组件
let ComponentToRender;
if (displayMode === 'albums') {
  ComponentToRender = AlbumGridDisplay;
} else if (displayMode === 'flat') {
  ComponentToRender = TrackListDisplay;
} else if (displayMode === 'poster') {
  ComponentToRender = ArtworkDisplay;
}
---

<MainLayout 
  title={pageTitle}
  description={pageDescription}
  type="music"
  canonical={canonicalUrl}
>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 && (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
              </svg>
            )}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-primary cursor-default' 
                  : 'text-text-secondary hover:text-primary'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    

    
    <!-- 所有分类 -->
    <section class="mb-8">
      <h1 class="text-3xl font-bold text-white mb-6">{categoryName}</h1>
      <h2 class="text-xl font-bold text-white mb-4">{t('common.categories', lang) || 'Categories'}</h2>
      <div class="flex flex-wrap gap-2">
        {(() => {
          // 按指定顺序排列有数据的分类
          const orderedCategories = ['unreleased', 'recent', 'best-of', 'art'];
          return [...allCategories].sort((a, b) => {
            const aIndex = orderedCategories.indexOf(a.id);
            const bIndex = orderedCategories.indexOf(b.id);
            
            // 如果两个都在有序列表中，按列表顺序排序
            if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
            
            // 如果只有一个在有序列表中，则它排在前面
            if (aIndex !== -1) return -1;
            if (bIndex !== -1) return 1;
            
            // 如果都不在有序列表中，按字母排序
            return a.name.localeCompare(b.name);
          }).map(category => {
            const hasData = ['unreleased', 'recent', 'best-of'].includes(category.id);
            const isActive = category.id === categoryId;

            return (
              <a
                href={`/artists/playboi-carti/${category.id}`}
                class={`px-3 py-1.5 rounded-md text-sm transition-all ${
                  isActive
                    ? 'bg-primary text-white font-medium'
                    : hasData
                      ? 'bg-dark-elevated hover:bg-dark-hover text-white hover:text-primary border border-primary/20'
                      : 'bg-dark-elevated hover:bg-dark-hover text-text-secondary hover:text-white opacity-60'
                }`}
              >
                {category.name}
                {hasData && !isActive && <span class="ml-1 text-primary text-xs">●</span>}
              </a>
            );
          });
        })()}
      </div>
    </section>

    <!-- Add Track Section (exclude unreleased category) - Moved to top for better visibility -->
    {categoryId !== 'unreleased' && (
      <CollapsibleSubmissionForm artistId={artistId} categoryId={categoryId} />
    )}

    {/* 动态渲染选定的组件 */}
    {!hasData ? (
      <div class="text-center py-16">
        <div class="max-w-lg mx-auto bg-dark-elevated p-8 rounded-xl border border-dark-hover shadow-lg">
          <div class="flex justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-white mb-4 text-center">Coming Soon</h2>
          <p class="text-text-secondary text-center">We're currently working on curating content for the <span class="text-primary font-semibold">{categoryName}</span> category. Check back soon for updates!</p>
        </div>
      </div>
    ) : ComponentToRender ? (
      displayMode === 'poster' ? (
        <ComponentToRender artworks={artworks} categoryId={categoryId} />
      ) : (
        <ComponentToRender albums={albums} tracks={tracks} categoryId={categoryId} artistId={artistId} />
      )
    ) : (
      <div class="text-center py-8">
        <p class="text-gray-400">Error: Display mode not configured or component not found.</p>
        <p class="text-gray-500 text-sm mt-2">Could not determine how to display content for this category.</p>
      </div>
    )}

  </main>
</MainLayout>

<script>
  // 等待DOM加载完成
  document.addEventListener('DOMContentLoaded', () => {
    // 获取所有播放按钮
    const playButtons = document.querySelectorAll('.play-button');
    
    // 为每个播放按钮添加点击事件监听器
    playButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        
        // 获取按钮上的数据属性
        const trackElement = button.closest('.track-item');
        if (!trackElement) return;
        
        const trackId = trackElement.getAttribute('data-track-id');
        const trackTitle = trackElement.getAttribute('data-track-title');
        const trackArtist = trackElement.getAttribute('data-track-artist');
        const trackArtwork = trackElement.getAttribute('data-track-artwork');
        const trackAudio = trackElement.getAttribute('data-track-audio');
        
        // 检查是否有音频链接
        if (!trackAudio) {
          console.error('No audio source found for this track');
          return;
        }
        
        // 触发自定义事件，传递歌曲信息给音频播放器组件
        const playEvent = new CustomEvent('play-track', {
          detail: {
            id: trackId,
            title: trackTitle,
            artist: trackArtist,
            artwork: trackArtwork,
            audio: trackAudio
          }
        });
        
        document.dispatchEvent(playEvent);
      });
    });
  });
</script>
