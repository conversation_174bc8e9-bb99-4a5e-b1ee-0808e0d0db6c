---
import MainLayout from '../../../layouts/MainLayout.astro';
import { promises as fs } from 'fs';
import path from 'path';
import SmartImage from '../../../components/SmartImage.astro';
import { t } from '../../../i18n';

import AlbumGridDisplay from '../../../components/categoryViews/AlbumGridDisplay.astro';
import TrackListDisplay from '../../../components/categoryViews/TrackListDisplay.astro';
import ArtworkDisplay from '../../../components/categoryViews/ArtworkDisplay.astro';
import CollapsibleSubmissionForm from '../../../components/content-submission/CollapsibleSubmissionForm.astro';
import type { Track } from '../../../types/Track';
import type { Artwork } from '../../../types/Artwork';

// Set English environment
const lang = 'en';

// Enable prerendering
export const prerender = true;

// Get all categories for static generation
export async function getStaticPaths() {
  // Define category list within the function to ensure consistency with ye.astro
  const yeCategories = [
    { id: 'album-copies', name: 'Album Copies' },
    { id: 'art', name: 'Art' },
    { id: 'best-of', name: 'Best Of' },
    { id: 'fakes', name: 'Fakes' },
    { id: 'grails', name: 'Grails' },
    { id: 'groupbuys', name: 'Groupbuys' },
    { id: 'misc', name: 'Misc' },
    { id: 'recent', name: 'Recent' },
    { id: 'released', name: 'Released' },
    { id: 'special', name: 'Special' },
    { id: 'stems', name: 'Stems' },
    { id: 'tracklists', name: 'Tracklists' },
    { id: 'unreleased', name: 'Unreleased' },
    { id: 'worst-of', name: 'Worst Of' }
  ];
  
  const paths = [];

  for (const category of yeCategories) {
    paths.push({
      params: { categoryId: category.id },
      props: { categoryName: category.name }
    });
  }

  return paths;
}

// Get specific category data
const { categoryId } = Astro.params;
const { categoryName } = Astro.props;
const artistId = 'ye';

// Define category list (ensure consistency with ye.astro and getStaticPaths)
const allCategories = [
  { id: 'album-copies', name: 'Album Copies' },
  { id: 'art', name: 'Art' },
  { id: 'best-of', name: 'Best Of' },
  { id: 'fakes', name: 'Fakes' },
  { id: 'grails', name: 'Grails' },
  { id: 'groupbuys', name: 'Groupbuys' },
  { id: 'misc', name: 'Misc' },
  { id: 'recent', name: 'Recent' },
  { id: 'released', name: 'Released' },
  { id: 'special', name: 'Special' },
  { id: 'stems', name: 'Stems' },
  { id: 'tracklists', name: 'Tracklists' },
  { id: 'unreleased', name: 'Unreleased' },
  { id: 'worst-of', name: 'Worst Of' }
];

// Breadcrumb navigation data - corrected order
const breadcrumbs = [
  { name: t('common.home', lang), url: '/' },
  { name: t('common.artists', lang), url: '/artists' },
  { name: 'Ye', url: '/artists/ye' },
  { name: t(`categories.${categoryId}`, lang) || categoryName, url: `/artists/ye/${categoryId}`, current: true }
];

// Load category info from JSON file
let categoryInfo = null;
let albums = [];

try {
  // Attempt to load category info
  const categoryInfoPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'info.json');
  
  try {
    const categoryInfoData = await fs.readFile(categoryInfoPath, 'utf-8');
    categoryInfo = JSON.parse(categoryInfoData);
  } catch (infoError) {
    console.error(`Category info not found for ${categoryId}:`, infoError);
  }

  // Get display mode from info.json, default to 'albums'
  const displayMode = categoryInfo?.displayMode || 'albums';

  // Only load eras.json in albums mode
  if (displayMode === 'albums') {
    try {
      const categoryErasPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');
      const erasData = await fs.readFile(categoryErasPath, 'utf-8');
      albums = JSON.parse(erasData);
      
      // Ensure each album has coverImage and backgroundColor fields, and check if it has data
      albums = await Promise.all(albums.map(async (album) => {
        // Check if this era has actual data files
        let hasData = false;
        try {
          const eraDataPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras', `${album.id}.json`);
          const eraData = await fs.readFile(eraDataPath, 'utf-8');
          const parsedEraData = JSON.parse(eraData);
          hasData = parsedEraData && parsedEraData.length > 0;
        } catch (error) {
          hasData = false;
        }

        return {
          ...album,
          coverImage: album.coverImage || '/images/eras/default.svg',
          backgroundColor: album.backgroundColor || '#333',
          hasData
        };
      }));
      
      // Sort albums by release order
      const eraOrder = [
        'before-the-college-dropout',
        'the-college-dropout',
        'late-registration',
        'graduation',
        '808s-heartbreak',
        'good-ass-job',
        'my-beautiful-dark-twisted-fantasy',
        'watch-the-throne',
        'cruel-summer',
        'thank-god-for-drugs',
        'yeezus',
        'cruel-winter-v1',
        'yeezus-2',
        'so-help-me-god',
        'swish',
        'the-life-of-pablo',
        'turbo-grafx-16',
        'cruel-winter-v2',
        'love-everyone',
        'ye',
        'kids-see-ghosts',
        'yandhi-v1',
        'yandhi-v2',
        'jesus-is-king',
        'jesus-is-lord',
        'donda-v1',
        'donda-v2',
        'donda-v3',
        'donda',
        'donda-2',
        'vultures-1',
        'vultures-2',
        'vultures-3',
        'vultures'
      ];
      
      albums.sort((a, b) => {
        let indexA = eraOrder.indexOf(a.id);
        let indexB = eraOrder.indexOf(b.id);
        
        // If not found in predefined order, place at the end
        if (indexA === -1) indexA = 999;
        if (indexB === -1) indexB = 999;
        
        return indexA - indexB;
      });
    } catch (erasError) {
      console.error(`Eras data not found for category ${categoryId}:`, erasError);
    }
  }
} catch (error) {
  console.error(`Error loading category data for ${categoryId}:`, error);
}

// Get display mode from info.json, default to 'albums'
const displayMode = categoryInfo?.displayMode || 'albums';

// Fallback data if JSON loading fails or is empty (for albums)
if (displayMode === 'albums' && (!albums || albums.length === 0)) {
  albums = [
    {
      id: 'the-college-dropout',
      name: 'The College Dropout',
      description: 'Debut studio album',
      trackCount: 21,
      backgroundColor: '#8B4513',
      coverImage: '/images/eras/default.jpg',
      artist: 'Ye',
      artistId: 'ye'
    }
  ];
}

// Attempt to load tracks or artwork data for the category
let tracks: Track[] = [];
let artworks: Artwork[] = [];
// Load tracks or artworks data
{
  try {
    const tracksPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'tracks.json');
    const tracksData = await fs.readFile(tracksPath, 'utf-8');
    const parsedData = JSON.parse(tracksData);
    
    if (displayMode === 'flat') {
      tracks = parsedData;
    } else if (displayMode === 'poster') {
      artworks = parsedData;
    }

    // Basic validation or transformation if needed
    if (!Array.isArray(tracks)) {
      console.error(`Invalid tracks data format for ${categoryId}: Expected an array.`);
      tracks = [];
    }
  } catch (tracksError) {
    // Only log error for flat or poster modes, as albums mode does not require tracks.json
    if (displayMode === 'flat' || displayMode === 'poster') {
      console.log(`Note: No tracks.json found for ${categoryId}, but this is only needed for flat/poster modes.`);
    }
    
    // Special handling for 'recent' category - attempt to parse HTML
    if (categoryId === 'recent') {
      // Placeholder for potential future HTML parsing logic
      // For now, we just log that it's not implemented
      try {
        const recentHtmlPath = path.join(process.cwd(), 'data', 'YeRecent.html');
        // await fs.access(recentHtmlPath); // Check if file exists
        // TODO: Implement HTML parsing logic here if needed
        // const htmlContent = await fs.readFile(recentHtmlPath, 'utf-8');
        // tracks = parseHtmlToTracks(htmlContent); 
        console.log('Found YeRecent.html, but parsing to tracks is not implemented yet.');
      } catch (recentError) {
        console.error('YeRecent.html processing error:', recentError);
      }
    }
  }
}

// Check if category has data
const hasData = ['art', 'best-of', 'recent', 'unreleased'].includes(categoryId);

// Determine which component to render based on displayMode
let ComponentToRender;
if (displayMode === 'albums') {
  ComponentToRender = AlbumGridDisplay;
} else if (displayMode === 'flat') {
  ComponentToRender = TrackListDisplay;
} else if (displayMode === 'poster') {
  ComponentToRender = ArtworkDisplay;
} // Add more else if for other display modes

---

<MainLayout title={`${categoryName} - Ye - TrackerHive`}>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb navigation -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 && (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
              </svg>
            )}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-primary cursor-default' 
                  : 'text-text-secondary hover:text-primary'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    

    
    <!-- All categories -->
    <section class="mb-8">
      <h1 class="text-2xl font-bold text-white mb-4">Categories</h1>
      <div class="flex flex-wrap gap-2">
        {(() => {
          // Sort categories with data in specified order, others alphabetically
          const orderedCategories = ['unreleased', 'recent', 'best-of', 'art'];
          return [...allCategories].sort((a, b) => {
            const aIndex = orderedCategories.indexOf(a.id);
            const bIndex = orderedCategories.indexOf(b.id);
            
            // If both are in the ordered list, sort by list order
            if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
            
            // If only one is in the ordered list, it comes first
            if (aIndex !== -1) return -1;
            if (bIndex !== -1) return 1;
            
            // If neither is in the ordered list, sort alphabetically
            return a.name.localeCompare(b.name);
          }).map(category => {
            const hasData = ['unreleased', 'recent', 'best-of', 'art'].includes(category.id);
            const isActive = category.id === categoryId;

            return (
              <a
                href={`/artists/ye/${category.id}`}
                class={`px-3 py-1.5 rounded-md text-sm transition-all ${
                  isActive
                    ? 'bg-primary text-white font-medium'
                    : hasData
                      ? 'bg-dark-elevated hover:bg-dark-hover text-white hover:text-primary border border-primary/20'
                      : 'bg-dark-elevated hover:bg-dark-hover text-text-secondary hover:text-white opacity-60'
                }`}
              >
                {category.name}
                {hasData && !isActive && <span class="ml-1 text-primary text-xs">●</span>}
              </a>
            );
          });
        })()}
      </div>
    </section>

    <!-- Add Track Section (exclude unreleased category) - Moved to top for better visibility -->
    {categoryId !== 'unreleased' && (
      <CollapsibleSubmissionForm artistId={artistId} categoryId={categoryId} />
    )}

    {/* Dynamically render the selected component */}
    {!hasData ? (
      <div class="text-center py-16">
        <div class="max-w-lg mx-auto bg-dark-elevated p-8 rounded-xl border border-dark-hover shadow-lg">
          <div class="flex justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-white mb-4 text-center">Coming Soon</h2>
          <p class="text-text-secondary text-center">We're currently working on curating content for the <span class="text-primary font-semibold">{categoryName}</span> category. Check back soon for updates!</p>
        </div>
      </div>
    ) : ComponentToRender ? (
      displayMode === 'poster' ? (
        <ComponentToRender artworks={artworks} categoryId={categoryId} />
      ) : (
        <ComponentToRender albums={albums} tracks={tracks} categoryId={categoryId} artistId={artistId} />
      )
    ) : (
      <div class="text-center py-8">
        <p class="text-gray-400">Error: Display mode not configured or component not found.</p>
        <p class="text-gray-500 text-sm mt-2">Could not determine how to display content for this category.</p>
      </div>
    )}

  </main>
</MainLayout>

<script>
  // Wait for DOM to load
  document.addEventListener('DOMContentLoaded', () => {
    // Get all play buttons
    const playButtons = document.querySelectorAll('.play-button');
    const audioPlayer = document.querySelector('#audio-player');
    
    if (!audioPlayer) return;
    
    // Add click event to each button
    playButtons.forEach(button => {
      button.addEventListener('click', function() {
        const audioUrl = this.getAttribute('data-audio-url');
        if (!audioUrl) return;
        
        // Reset all button states
        playButtons.forEach(btn => {
          btn.classList.remove('playing');
          btn.querySelector('.play-icon').classList.remove('hidden');
          btn.querySelector('.pause-icon').classList.add('hidden');
          btn.querySelector('.loading-icon').classList.add('hidden');
        });
        
        // Show loading state for current button
        this.querySelector('.play-icon').classList.add('hidden');
        this.querySelector('.loading-icon').classList.remove('hidden');
        
        // Set audio source and play
        audioPlayer.src = audioUrl;
        audioPlayer.play().catch(error => {
          console.error('Playback failed:', error);
          // Restore button state
          this.querySelector('.loading-icon').classList.add('hidden');
          this.querySelector('.play-icon').classList.remove('hidden');
        });
      });
    });
    
    // Listen for audio canplay event
    audioPlayer.addEventListener('canplay', function() {
      // Find currently loading button
      const loadingButton = document.querySelector('.play-button .loading-icon:not(.hidden)')?.closest('.play-button');
      if (loadingButton) {
        // Update button state to playing
        loadingButton.classList.add('playing');
        loadingButton.querySelector('.loading-icon').classList.add('hidden');
        loadingButton.querySelector('.pause-icon').classList.remove('hidden');
      }
    });
    
    // Listen for audio ended event
    audioPlayer.addEventListener('ended', function() {
      // Reset all button states
      playButtons.forEach(btn => {
        btn.classList.remove('playing');
        btn.querySelector('.play-icon').classList.remove('hidden');
        btn.querySelector('.pause-icon').classList.add('hidden');
        btn.querySelector('.loading-icon').classList.add('hidden');
      });
    });
    
    // Listen for audio pause event
    audioPlayer.addEventListener('pause', function() {
      if (!this.ended) {
        // Find currently playing button
        const playingButton = document.querySelector('.play-button.playing');
        if (playingButton) {
          // Update button state to paused
          playingButton.classList.remove('playing');
          playingButton.querySelector('.pause-icon').classList.add('hidden');
          playingButton.querySelector('.play-icon').classList.remove('hidden');
        }
      }
    });
    
    // Listen for audio play event
    audioPlayer.addEventListener('play', function() {
      // Find currently playing button
      const playingButton = document.querySelector('.play-button.playing');
      if (playingButton) {
        // Ensure pause icon is shown
        playingButton.querySelector('.play-icon').classList.add('hidden');
        playingButton.querySelector('.pause-icon').classList.remove('hidden');
        playingButton.querySelector('.loading-icon').classList.add('hidden');
      }
    });
  });
</script>
