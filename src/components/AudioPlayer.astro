---
// 音频播放器组件
import { t } from '../i18n';
---

<div id="audio-player" class="fixed bottom-0 left-0 right-0 bg-black p-3 sm:p-4 z-50">
  <!-- 桌面视图 -->
  <div class="th-desktop-view" style="margin-left: 240px;">
    <div class="max-w-7xl mx-auto flex items-center justify-between sm:justify-center">
      <!-- 左侧：专辑封面和信息 -->
      <div class="flex items-center">
        <div class="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gray-800 rounded-md overflow-hidden mr-2 sm:mr-4 border border-gray-600">
          <img id="player-cover" src="/images/album-placeholder.svg" alt="Album cover" class="w-full h-full object-cover" data-current-src="/images/album-placeholder.svg" loading="lazy">
        </div>
        
        <script is:inline>
          // 立即防止图片更新
          (function() {
            // 锁定图片源，防止任何代码修改它
            const playerCover = document.getElementById('player-cover');
            if (playerCover) {
              const originalSrc = playerCover.src;
              // 保存原始的setAttribute方法
              const originalSetAttribute = playerCover.setAttribute;
              
              // 重写setAttribute方法，忽略src和data-current-src的设置
              playerCover.setAttribute = function(name, value) {
                if (name === 'src' || name === 'data-current-src') {
                  // 忽略这些属性的更改
                  console.log('防止图片属性更新:', name, value);
                  return;
                }
                // 对其他属性使用原始方法
                return originalSetAttribute.call(this, name, value);
              };
              
              // 定义不可修改的src属性
              Object.defineProperty(playerCover, 'src', {
                get: function() { return originalSrc; },
                set: function() { /* 忽略设置 */ },
                configurable: false
              });
            }
          })();
        </script>
        
        <!-- 在移动端显示歌曲信息，桌面端隐藏 -->
        <div class="sm:hidden flex-grow mr-2 max-w-[60%]">
          <div class="flex justify-between items-center">
            <div class="w-full">
              <p id="player-title" class="text-white text-sm font-medium truncate">&nbsp;</p>
              <p id="player-artist" class="text-gray-400 text-xs truncate">&nbsp;</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 桌面端显示的歌曲信息和进度条 - 独立布局 -->
      <div class="hidden sm:block flex-grow mx-4 max-w-[40%]">
        <div class="flex justify-between items-center mb-1">
          <div class="w-full">
            <p id="player-title-desktop" class="text-white text-base font-medium truncate">&nbsp;</p>
            <p id="player-artist-desktop" class="text-gray-400 text-sm truncate">&nbsp;</p>
          </div>
        </div>
        
        <div class="flex items-center">
          <div class="flex-grow relative h-1 bg-gray-700 rounded-full overflow-hidden">
            <div id="progress-bar" class="absolute left-0 top-0 bottom-0 bg-primary w-0 transition-all duration-300"></div>
            <!-- 加载进度条，位于播放进度条上方 -->
            <div id="loading-progress-bar" class="absolute left-0 top-0 bottom-0 bg-primary/30 w-0 transition-all duration-300"></div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：控制按钮 -->
      <div class="flex items-center space-x-1 sm:space-x-4">
        <button id="prev-button" class="text-gray-400 hover:text-white focus:outline-none p-1 sm:p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 20 20" fill="currentColor">
            <path d="M8.445 14.832A1 1 0 0010 14v-2.798l5.445 3.63A1 1 0 0017 14V6a1 1 0 00-1.555-.832L10 8.798V6a1 1 0 00-1.555-.832l-6 4a1 1 0 000 1.664l6 4z" />
          </svg>
        </button>
        
        <button id="play-pause-button" class="bg-primary text-white rounded-full p-1.5 sm:p-2 focus:outline-none hover:bg-primary-dark relative">
          <!-- 加载指示器 -->
          <div id="loading-indicator" class="absolute inset-0 flex items-center justify-center bg-primary rounded-full opacity-0 transition-opacity duration-300">
            <!-- 移除旋转动画，只保留百分比显示 -->
            <span id="loading-percentage" class="absolute text-[10px] sm:text-[12px] font-bold text-white">0%</span>
          </div>
          
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 20 20" fill="currentColor" id="play-icon">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 hidden" viewBox="0 0 20 20" fill="currentColor" id="pause-icon">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </button>
        
        <button id="next-button" class="text-gray-400 hover:text-white focus:outline-none p-1 sm:p-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4.555 5.168A1 1 0 003 6v8a1 1 0 001.555.832L10 11.202V14a1 1 0 001.555.832l6-4a1 1 0 000-1.664l-6-4A1 1 0 0010 6v2.798L4.555 5.168z" />
          </svg>
        </button>
        
        <!-- 音量控制 - 只在桌面端显示 -->
        <div class="hidden sm:flex items-center space-x-1 sm:space-x-2 ml-1 sm:ml-4">
          <button id="volume-toggle" class="text-gray-400 hover:text-white focus:outline-none p-1 sm:p-2">
            <svg id="volume-high-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
            </svg>
            <svg id="volume-medium-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 hidden" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
            </svg>
            <svg id="volume-low-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 hidden" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
            </svg>
            <svg id="volume-mute-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6 hidden" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <line x1="1" y1="1" x2="23" y2="23"></line>
              <path d="M9 9v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2z"></path>
              <path d="M11 9l5-5v16l-5-5"></path>
            </svg>
          </button>
          <div id="volume-slider" class="w-12 sm:w-24 bg-gray-700 rounded-full h-1.5 sm:h-2 cursor-pointer relative overflow-hidden">
            <div id="volume-level" class="absolute left-0 top-0 bottom-0 bg-primary w-[70%] transition-all duration-300"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 移动端进度条 - 放在底部 -->
    <div class="sm:hidden w-full mt-2">
      <div class="flex-grow relative h-0.5 bg-gray-700 rounded-full overflow-hidden">
        <div id="mobile-progress-bar" class="absolute left-0 top-0 bottom-0 bg-primary w-0 transition-all duration-300"></div>
        <div id="mobile-loading-progress-bar" class="absolute left-0 top-0 bottom-0 bg-primary/30 w-0 transition-all duration-300"></div>
      </div>
    </div>
  </div>
  
  <!-- 移动端布局 -->
  <div class="th-mobile-view">
    <div class="max-w-7xl mx-auto flex items-center justify-between sm:justify-center">
      <!-- 左侧：专辑封面和信息 -->
      <div class="flex items-center">
        <div class="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gray-800 rounded-md overflow-hidden mr-2 sm:mr-4 border border-gray-600">
          <img id="player-cover-mobile" src="/images/album-placeholder.svg" alt="Album cover" class="w-full h-full object-cover" data-current-src="/images/album-placeholder.svg" loading="lazy">
        </div>
        
        <!-- 在移动端显示歌曲信息 -->
        <div class="flex-grow mr-2 max-w-[60%]">
          <div class="flex justify-between items-center">
            <div class="w-full">
              <p id="player-title-mobile" class="text-white text-sm font-medium truncate">&nbsp;</p>
              <p id="player-artist-mobile" class="text-gray-400 text-xs truncate">&nbsp;</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：控制按钮 -->
      <div class="flex items-center space-x-1">
        <button id="prev-button-mobile" class="text-gray-400 hover:text-white focus:outline-none p-1">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M8.445 14.832A1 1 0 0010 14v-2.798l5.445 3.63A1 1 0 0017 14V6a1 1 0 00-1.555-.832L10 8.798V6a1 1 0 00-1.555-.832l-6 4a1 1 0 000 1.664l6 4z" />
          </svg>
        </button>
        
        <button id="play-pause-button-mobile" class="bg-primary text-white rounded-full p-1.5 focus:outline-none hover:bg-primary-dark relative">
          <!-- 加载指示器 -->
          <div id="loading-indicator-mobile" class="absolute inset-0 flex items-center justify-center bg-primary rounded-full opacity-0 transition-opacity duration-300">
            <span id="loading-percentage-mobile" class="absolute text-[10px] font-bold text-white">0%</span>
          </div>
          
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" id="play-icon-mobile">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 hidden" viewBox="0 0 20 20" fill="currentColor" id="pause-icon-mobile">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </button>
        
        <button id="next-button-mobile" class="text-gray-400 hover:text-white focus:outline-none p-1">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M4.555 5.168A1 1 0 003 6v8a1 1 0 001.555.832L10 11.202V14a1 1 0 001.555.832l6-4a1 1 0 000-1.664l-6-4A1 1 0 0010 6v2.798L4.555 5.168z" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 移动端进度条 - 放在底部 -->
    <div class="w-full mt-2">
      <div class="flex-grow relative h-0.5 bg-gray-700 rounded-full overflow-hidden">
        <div id="mobile-progress-bar-2" class="absolute left-0 top-0 bottom-0 bg-primary w-0 transition-all duration-300"></div>
        <div id="mobile-loading-progress-bar-2" class="absolute left-0 top-0 bottom-0 bg-primary/30 w-0 transition-all duration-300"></div>
      </div>
    </div>
  </div>
  
  <audio id="audio-element" class="hidden">
    <!-- 添加进度事件监听 -->
    <script>
      document.getElementById('audio-element').addEventListener('progress', (e) => {
        const loadingProgressBar = document.getElementById('loading-progress-bar');
        const mobileLoadingProgressBar = document.getElementById('mobile-loading-progress-bar');
        const mobileLoadingProgressBar2 = document.getElementById('mobile-loading-progress-bar-2');
        if (loadingProgressBar) {
          const loaded = e.target.buffered.length ? e.target.buffered.end(0) / e.target.duration : 0;
          loadingProgressBar.style.width = `${loaded * 100}%`;
          if (mobileLoadingProgressBar) {
            mobileLoadingProgressBar.style.width = `${loaded * 100}%`;
          }
          if (mobileLoadingProgressBar2) {
            mobileLoadingProgressBar2.style.width = `${loaded * 100}%`;
          }
        }
      });
    </script>
  </audio>
</div>

<!-- 使用Canvas绘制时间显示，完全避免DOM更新 -->
<canvas id="time-display-canvas" width="120" height="30" class="fixed bottom-0 left-0 right-0 mx-auto z-50 mb-16 sm:mb-20" style="background-color: transparent; border-radius: 4px; max-width: 120px;"></canvas>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const audioPlayer = document.getElementById('audio-player');
    const audioElement = document.getElementById('audio-element');
    const playPauseButton = document.getElementById('play-pause-button');
    const playIcon = document.getElementById('play-icon');
    const pauseIcon = document.getElementById('pause-icon');
    const prevButton = document.getElementById('prev-button');
    const nextButton = document.getElementById('next-button');
    const progressBar = document.getElementById('progress-bar');
    const mobileProgressBar = document.getElementById('mobile-progress-bar');
    const mobileProgressBar2 = document.getElementById('mobile-progress-bar-2');
    const loadingProgressBar = document.getElementById('loading-progress-bar');
    const loadingIndicator = document.getElementById('loading-indicator');
    const playerTitle = document.getElementById('player-title');
    const playerArtist = document.getElementById('player-artist');
    const playerTitleDesktop = document.getElementById('player-title-desktop');
    const playerArtistDesktop = document.getElementById('player-artist-desktop');
    const playerTitleMobile = document.getElementById('player-title-mobile');
    const playerArtistMobile = document.getElementById('player-artist-mobile');
    const playerCover = document.getElementById('player-cover');
    const currentTimeDisplay = document.getElementById('current-time');
    const durationDisplay = document.getElementById('duration');
    
    // 移动端控制按钮
    const playPauseButtonMobile = document.getElementById('play-pause-button-mobile');
    const playIconMobile = document.getElementById('play-icon-mobile');
    const pauseIconMobile = document.getElementById('pause-icon-mobile');
    const prevButtonMobile = document.getElementById('prev-button-mobile');
    const nextButtonMobile = document.getElementById('next-button-mobile');
    const loadingIndicatorMobile = document.getElementById('loading-indicator-mobile');
    const loadingPercentageMobile = document.getElementById('loading-percentage-mobile');
    
    // 音量控制元素
    const volumeToggle = document.getElementById('volume-toggle');
    const volumeSlider = document.getElementById('volume-slider');
    const volumeLevel = document.getElementById('volume-level');
    const volumeHighIcon = document.getElementById('volume-high-icon');
    const volumeMediumIcon = document.getElementById('volume-medium-icon');
    const volumeLowIcon = document.getElementById('volume-low-icon');
    const volumeMuteIcon = document.getElementById('volume-mute-icon');
    
    // 默认音量 (70%)
    const DEFAULT_VOLUME = 0.7;
    // 上次非静音音量
    let lastVolume = DEFAULT_VOLUME;
    
    // 加载状态管理
    let isLoading = false;
    let loadingTimeout = null;
    let loadingInterval = null;
    let simulatedProgress = 0;
    const TOTAL_LOADING_TIME = 12000; // 6秒加载时间
    const UPDATE_INTERVAL = 100; // 每100ms更新一次
    const loadingPercentage = document.getElementById('loading-percentage');
    
    // 显示加载状态 - 改进版本，区分初始加载和播放中的缓冲
    function showLoading(isInitialLoad = false) {
      isLoading = true;

      // 桌面端加载指示器
      if (loadingIndicator) {
        loadingIndicator.classList.remove('opacity-0');
      }

      // 移动端加载指示器
      if (loadingIndicatorMobile) {
        loadingIndicatorMobile.classList.remove('opacity-0');
      }

      // 只在初始加载时重置模拟进度，播放中的缓冲不重置
      if (isInitialLoad || !window.audioPlayerState.isPlaying) {
        simulatedProgress = 0;
        updateLoadingPercentage(0);

        // 清除之前的定时器
        if (loadingInterval) {
          clearInterval(loadingInterval);
        }

        // 启动模拟进度更新
        loadingInterval = setInterval(() => {
          if (simulatedProgress < 99) {
            // 计算每次更新应增加的进度（确保6秒内达到99%）
            const increment = (99 / (TOTAL_LOADING_TIME / UPDATE_INTERVAL));
            simulatedProgress = Math.min(99, simulatedProgress + increment);
            updateLoadingPercentage(Math.floor(simulatedProgress));
          }
        }, UPDATE_INTERVAL);
      }

      // 设置超时，如果30秒后仍在加载，则显示进度条
      loadingTimeout = setTimeout(() => {
        if (isLoading) {
          // 可以在这里添加额外的UI反馈，比如显示"加载时间较长"的提示
        }
      }, 30000);
    }
    
    // 更新加载百分比显示
    function updateLoadingPercentage(percentage) {
      if (loadingPercentage) {
        loadingPercentage.textContent = `${percentage}%`;
      }
      if (loadingPercentageMobile) {
        loadingPercentageMobile.textContent = `${percentage}%`;
      }
    }
    
    // 隐藏加载状态
    function hideLoading() {
      // 设置为100%后再隐藏
      updateLoadingPercentage(100);
      
      // 延迟一小段时间后隐藏，让用户看到100%
      setTimeout(() => {
        isLoading = false;
        
        // 桌面端加载指示器
        if (loadingIndicator) {
          loadingIndicator.classList.add('opacity-0');
        }
        
        // 移动端加载指示器
        if (loadingIndicatorMobile) {
          loadingIndicatorMobile.classList.add('opacity-0');
        }
        
        if (loadingTimeout) {
          clearTimeout(loadingTimeout);
          loadingTimeout = null;
        }
        
        if (loadingInterval) {
          clearInterval(loadingInterval);
          loadingInterval = null;
        }
      }, 300);
    }
    
    // 播放暂停切换
    playPauseButton.addEventListener('click', () => {
      if (window.audioPlayerState.isPlaying) {
        audioElement.pause();
        // 触发暂停事件
        document.dispatchEvent(new CustomEvent('audio-player-action', {
          detail: { action: 'pause' }
        }));
      } else {
        // 恢复播放时不重置进度，只显示加载指示器
        showLoading(false);
        audioElement.play().then(() => {
          // 播放成功后隐藏加载状态
          hideLoading();
          // 触发播放事件
          document.dispatchEvent(new CustomEvent('audio-player-action', {
            detail: { action: 'play' }
          }));
        }).catch(error => {
          // 播放失败，隐藏加载状态
          hideLoading();
        });
      }
    });
    
    // 移动端播放暂停切换
    if (playPauseButtonMobile) {
      playPauseButtonMobile.addEventListener('click', () => {
        if (window.audioPlayerState.isPlaying) {
          audioElement.pause();
          // 触发暂停事件
          document.dispatchEvent(new CustomEvent('audio-player-action', {
            detail: { action: 'pause' }
          }));
        } else {
          // 恢复播放时不重置进度，只显示加载指示器
          showLoading(false);
          audioElement.play().then(() => {
            // 播放成功后隐藏加载状态
            hideLoading();
            // 触发播放事件
            document.dispatchEvent(new CustomEvent('audio-player-action', {
              detail: { action: 'play' }
            }));
          }).catch(error => {
            // 播放失败，隐藏加载状态
            hideLoading();
          });
        }
      });
    }
    
    // 上一首
    prevButton.addEventListener('click', () => {
      playPreviousTrack();
    });
    
    // 移动端上一首
    if (prevButtonMobile) {
      prevButtonMobile.addEventListener('click', () => {
        playPreviousTrack();
      });
    }
    
    // 下一首
    nextButton.addEventListener('click', () => {
      playNextTrack();
    });
    
    // 移动端下一首
    if (nextButtonMobile) {
      nextButtonMobile.addEventListener('click', () => {
        playNextTrack();
      });
    }
    
    // 进度条点击
    progressBar.parentElement.addEventListener('click', (e) => {
      const progressBarWidth = progressBar.parentElement.offsetWidth;
      const clickPosition = e.offsetX;
      const percentageClicked = clickPosition / progressBarWidth;
      
      if (audioElement.duration) {
        audioElement.currentTime = audioElement.duration * percentageClicked;
      }
    });
    
    // 音频加载事件监听 - 改进版本，区分不同的加载状态
    audioElement.addEventListener('loadstart', () => {
      // loadstart 通常表示开始加载新的音频源，这是初始加载
      showLoading(true);
    });

    audioElement.addEventListener('waiting', () => {
      // waiting 表示播放中的缓冲，不重置进度
      if (!window.audioPlayerState.isPlaying) {
        showLoading(false);
      } else {
        // 播放中的缓冲，只显示加载指示器，不重置进度
        if (loadingIndicator) {
          loadingIndicator.classList.remove('opacity-0');
        }
        if (loadingIndicatorMobile) {
          loadingIndicatorMobile.classList.remove('opacity-0');
        }
      }
    });

    audioElement.addEventListener('stalled', () => {
      // stalled 表示网络停滞，播放中不重置进度
      if (!window.audioPlayerState.isPlaying) {
        showLoading(false);
      } else {
        // 播放中的网络停滞，只显示加载指示器
        if (loadingIndicator) {
          loadingIndicator.classList.remove('opacity-0');
        }
        if (loadingIndicatorMobile) {
          loadingIndicatorMobile.classList.remove('opacity-0');
        }
      }
    });

    audioElement.addEventListener('suspend', () => {
      // 只有在暂停状态下才隐藏加载指示器
      if (audioElement.readyState >= 3) {
        hideLoading();
      }
    });
    
    audioElement.addEventListener('canplay', () => {
      hideLoading();
    });
    
    audioElement.addEventListener('canplaythrough', () => {
      hideLoading();
    });
    
    // 定期更新缓冲进度
    audioElement.addEventListener('progress', () => {
      updateLoadingProgress();
    });
    
    // 音频播放状态变化
    audioElement.addEventListener('play', () => {
      window.audioPlayerState.isPlaying = true;
      
      // 桌面端图标更新
      if (playIcon) playIcon.classList.add('hidden');
      if (pauseIcon) pauseIcon.classList.remove('hidden');
      
      // 移动端图标更新
      if (playIconMobile) playIconMobile.classList.add('hidden');
      if (pauseIconMobile) pauseIconMobile.classList.remove('hidden');
      
      // 播放时保存状态
      savePlayerState();
    });
    
    audioElement.addEventListener('pause', () => {
      window.audioPlayerState.isPlaying = false;
      
      // 桌面端图标更新
      if (playIcon) playIcon.classList.remove('hidden');
      if (pauseIcon) pauseIcon.classList.add('hidden');
      
      // 移动端图标更新
      if (playIconMobile) playIconMobile.classList.remove('hidden');
      if (pauseIconMobile) pauseIconMobile.classList.add('hidden');
      
      hideLoading(); // 确保在暂停时隐藏加载状态
      
      // 暂停时保存状态
      savePlayerState();
    });
    
    // 时间更新 - 使用节流处理
    // 记录上次更新时间和上次保存状态的时间
    let lastUpdateTime = 0;
    let lastSaveStateTime = 0;

    // 上次更新时的时间值，用于避免重复更新相同的值
    let lastTimeDisplayValue = '';

    // 记录上次的播放进度，用于防止意外重置
    let lastKnownProgress = 0;
    
    // 使用Canvas绘制时间显示，完全避免DOM更新
    function updateTimeDisplay(currentTime, duration) {
      const timeDisplay = formatTime(currentTime);
      const durationDisplay = formatTime(duration);
      
      // 只在时间真正变化时才重绘Canvas
      if (timeDisplay === lastTimeDisplayValue) return;
      lastTimeDisplayValue = timeDisplay;
      
      // 使用Canvas绘制时间
      const canvas = document.getElementById('time-display-canvas');
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // 设置文本样式 - 使用白色字体和更大的字号
      ctx.font = 'bold 14px Arial';
      ctx.fillStyle = '#ffffff'; // 白色文本，更显眼
      ctx.textAlign = 'center'; // 文本居中
      
      // 绘制时间文本
      ctx.fillText(timeDisplay + ' / ' + durationDisplay, canvas.width/2, 20);
    }
    
    audioElement.addEventListener('timeupdate', () => {
      const now = Date.now();
      const currentTime = audioElement.currentTime;
      const duration = audioElement.duration || 0;

      // 减少更新间隔到 100 毫秒，使进度条更新更平滑
      // 或者当接近结束时（剩余时间小于1秒）强制更新
      const isNearEnd = duration - currentTime < 1;

      if (now - lastUpdateTime >= 100 || isNearEnd) {
        // 更新进度条 - 改进版本，确保进度条连续性
        if (duration > 0 && currentTime >= 0) {  // 确保duration和currentTime都有效
          const percentage = Math.min(100, Math.max(0, (currentTime / duration) * 100));

          // 改进的进度条更新逻辑：
          // 1. 如果是正常播放进度（向前），直接更新
          // 2. 如果进度向后跳跃超过5%，可能是重置，需要检查
          // 3. 如果差异很小（<1%），可能是精度问题，允许更新
          if (progressBar) {
            const currentWidth = parseFloat(progressBar.style.width) || 0;
            const diff = percentage - currentWidth;

            // 允许更新的条件：
            // - 进度向前 (diff >= 0)
            // - 或者向后跳跃很小 (diff > -5)
            // - 或者是接近结束时的精确更新
            if (diff >= 0 || diff > -5 || isNearEnd) {
              progressBar.style.width = `${percentage}%`;
              lastKnownProgress = percentage; // 记录最后的有效进度
            }
          }

          // 同样处理移动端进度条
          if (mobileProgressBar) {
            const currentWidth = parseFloat(mobileProgressBar.style.width) || 0;
            const diff = percentage - currentWidth;
            if (diff >= 0 || diff > -5 || isNearEnd) {
              mobileProgressBar.style.width = `${percentage}%`;
            }
          }

          if (mobileProgressBar2) {
            const currentWidth = parseFloat(mobileProgressBar2.style.width) || 0;
            const diff = percentage - currentWidth;
            if (diff >= 0 || diff > -5 || isNearEnd) {
              mobileProgressBar2.style.width = `${percentage}%`;
            }
          }
        }

        // 使用单独的函数更新分离的时间显示
        // 这不会触发播放器组件的重绘
        setTimeout(() => {
          updateTimeDisplay(currentTime, duration);
        }, 0);

        lastUpdateTime = now;
      }

      // 不再保存状态
      window.audioPlayerState.currentTime = currentTime;
    });
    
    // 音频加载完成
    audioElement.addEventListener('loadedmetadata', () => {
      // 使用Canvas更新时间显示，而不是修改DOM
      updateTimeDisplay(audioElement.currentTime, audioElement.duration);
    });
    
    // 音频播放结束
    audioElement.addEventListener('ended', () => {
      // 停止播放，不自动播放下一首
      window.audioPlayerState.isPlaying = false;
      
      // 更新播放/暂停按钮状态
      if (playIcon) playIcon.classList.remove('hidden');
      if (pauseIcon) pauseIcon.classList.add('hidden');
      
      // 在结束时强制更新时间显示到最后一秒
      const duration = audioElement.duration || 0;
      updateTimeDisplay(duration, duration);
      
      // 重置进度条 - 保持在100%而不是重置到0%
      if (progressBar) progressBar.style.width = '100%';
      if (mobileProgressBar) mobileProgressBar.style.width = '100%';
      if (mobileProgressBar2) mobileProgressBar2.style.width = '100%';
      
      // 触发播放结束事件
      document.dispatchEvent(new CustomEvent('audio-player-action', {
        detail: { action: 'ended', track: window.audioPlayerState.currentTrack }
      }));
      
      // 保存状态
      savePlayerState();
    });
    
    // 错误处理
    audioElement.addEventListener('error', (e) => {
      hideLoading();

      // 如果当前曲目有originalUrl，尝试使用它作为备选
      const currentTrack = window.audioPlayerState.currentTrack;
      if (currentTrack && currentTrack.originalUrl && audioElement.src !== currentTrack.originalUrl) {
        // 尝试使用originalUrl
        audioElement.src = currentTrack.originalUrl;
        audioElement.play().catch(() => {
          // 如果originalUrl也失败，显示错误提示
          alert(t('track.playbackFailed'));
        });
      } else {
        // 显示错误提示
        alert(t('track.playbackFailed'));
      }
    });
    
    // 设置默认音量
    setVolume(DEFAULT_VOLUME);
    
    // 音量图标点击事件 - 静音/取消静音
    volumeToggle.addEventListener('click', () => {
      if (audioElement.volume > 0) {
        // 当前有声音，设置为静音
        lastVolume = audioElement.volume;
        setVolume(0);
      } else {
        // 当前静音，恢复到上次音量
        setVolume(lastVolume);
      }
    });
    
    // 音量滑块点击事件
    volumeSlider.addEventListener('click', (e) => {
      const sliderWidth = volumeSlider.clientWidth;
      const clickPosition = e.clientX - volumeSlider.getBoundingClientRect().left;
      const newVolume = Math.max(0, Math.min(1, clickPosition / sliderWidth));
      setVolume(newVolume);
    });
    
    // 设置音量并更新UI
    function setVolume(volume) {
      // 确保音量在0-1范围内
      volume = Math.max(0, Math.min(1, volume));
      
      // 设置音频元素音量
      audioElement.volume = volume;
      
      // 更新音量滑块UI
      volumeLevel.style.width = `${volume * 100}%`;
      
      // 更新音量图标
      updateVolumeIcon(volume);

      // 触发音量改变事件
      document.dispatchEvent(new CustomEvent('audio-player-action', {
        detail: { 
          action: 'volume',
          volume: volume
        }
      }));
    }
    
    // 更新音量图标
    function updateVolumeIcon(volume) {
      // 隐藏所有图标
      [volumeHighIcon, volumeMediumIcon, volumeLowIcon, volumeMuteIcon].forEach(icon => {
        if (icon) icon.classList.add('hidden');
      });
      
      // 显示对应音量的图标
      if (volume === 0) {
        volumeMuteIcon.classList.remove('hidden');
      } else if (volume < 0.3) {
        volumeLowIcon.classList.remove('hidden');
      } else if (volume < 0.7) {
        volumeMediumIcon.classList.remove('hidden');
      } else {
        volumeHighIcon.classList.remove('hidden');
      }
    }
    
    // 格式化时间显示 (秒 -> mm:ss)
    function formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
    }
    
    // 播放指定曲目
    window.playTrack = (track, playlist = [], index = 0) => {
      // 优先使用 url 字段，其次使用 audioUrl 字段
      if (!track || (!track.url && !track.audioUrl)) return;
      
      // 确保曲目对象包含所有必要字段
      if (!track.artist && track.artists) {
        if (Array.isArray(track.artists)) {
          track.artist = track.artists.join(', ');
        } else if (typeof track.artists === 'string') {
          track.artist = track.artists;
        }
      }
      
      // 确保有 name 字段
      if (!track.name && track.title) {
        track.name = track.title;
      }
      
      // 确保有 title 字段
      if (!track.title && track.name) {
        track.title = track.name;
      }
      
      // 确保有 id 字段
      if (!track.id) {
        track.id = `track-${Date.now()}`;
      }
      
      // 确保 url 和 audioUrl 字段同步
      if (track.url && !track.audioUrl) {
        track.audioUrl = track.url;
      } else if (track.audioUrl && !track.url) {
        track.url = track.audioUrl;
      }
      

      
      // 设置音频源
      const audioUrl = track.url || track.audioUrl;

      // 尝试处理pillowcase.su API的访问问题
      if (audioUrl && audioUrl.includes('api.pillowcase.su')) {
        // 对于pillowcase.su API，我们可能需要特殊处理
        // 但HTML5 audio元素不支持自定义headers，所以直接设置
        audioElement.src = audioUrl;

        // 设置crossOrigin属性尝试解决CORS问题
        audioElement.crossOrigin = 'anonymous';
      } else {
        audioElement.src = audioUrl;
      }

      // 重要：重置当前播放时间，确保从头开始播放
      audioElement.currentTime = 0;

      // 重置进度记录
      lastKnownProgress = 0;

      // 立即重置所有进度条的视觉显示
      if (progressBar) progressBar.style.width = '0%';
      if (mobileProgressBar) mobileProgressBar.style.width = '0%';
      if (mobileProgressBar2) mobileProgressBar2.style.width = '0%';
      
      // 更新播放器信息
      updatePlayerInfo(track);
      
      // 更新当前播放状态
      window.audioPlayerState.currentTrack = track;
      window.audioPlayerState.playlist = playlist.length > 0 ? playlist : [track];
      window.audioPlayerState.currentIndex = index;
      
      // 显示加载状态 - 播放新曲目时是初始加载
      showLoading(true);

      // 开始播放
      audioElement.play().then(() => {
        // 播放成功后隐藏加载状态
        hideLoading();

        // 触发播放事件
        document.dispatchEvent(new CustomEvent('audio-player-action', {
          detail: { action: 'play', track }
        }));
      }).catch(error => {
        // 播放失败，隐藏加载状态
        hideLoading();

        // 显示错误提示
        alert(t('track.playbackFailed'));
      });
      
      // 保存播放器状态
      savePlayerState();
      
      return track;
    };
    
    // 播放下一首
    function playNextTrack() {
      const { playlist, currentIndex } = window.audioPlayerState;
      
      // 首先尝试从播放列表中获取下一首
      if (playlist && playlist.length > 1) {
        const nextIndex = (currentIndex + 1) % playlist.length;
        window.playTrack(playlist[nextIndex], playlist, nextIndex);
        return;
      }
      
      // 如果没有播放列表或只有一首歌，从播放历史中获取
      const history = getPlayHistory();
      
      // 找到当前播放曲目在历史中的位置
      const currentTrack = window.audioPlayerState.currentTrack;
      if (!currentTrack || history.length <= 1) return;
      
      // 在历史中查找当前曲目的索引
      const currentHistoryIndex = history.findIndex(track => 
        track.name === currentTrack.name && track.artist === currentTrack.artist
      );
      
      // 如果找到当前曲目，播放历史中的下一首（更早播放的）
      if (currentHistoryIndex !== -1 && currentHistoryIndex < history.length - 1) {
        const nextTrack = history[currentHistoryIndex + 1];
        window.playTrack(nextTrack, [nextTrack], 0);
      }
    }
    
    // 播放上一首
    function playPreviousTrack() {
      const { playlist, currentIndex } = window.audioPlayerState;
      
      // 首先尝试从播放列表中获取上一首
      if (playlist && playlist.length > 1) {
        const prevIndex = (currentIndex - 1 + playlist.length) % playlist.length;
        window.playTrack(playlist[prevIndex], playlist, prevIndex);
        return;
      }
      
      // 如果没有播放列表或只有一首歌，从播放历史中获取
      const history = getPlayHistory();
      
      // 找到当前播放曲目在历史中的位置
      const currentTrack = window.audioPlayerState.currentTrack;
      if (!currentTrack || history.length <= 1) return;
      
      // 在历史中查找当前曲目的索引
      const currentHistoryIndex = history.findIndex(track => 
        track.name === currentTrack.name && track.artist === currentTrack.artist
      );
      
      // 如果找到当前曲目，播放历史中的上一首（最近播放的）
      if (currentHistoryIndex !== -1 && currentHistoryIndex > 0) {
        const prevTrack = history[currentHistoryIndex - 1];
        window.playTrack(prevTrack, [prevTrack], 0);
      }
    }
    
    // 全局事件监听
    document.addEventListener('play-track', (e) => {
      const { track, playlist, index } = e.detail;
      window.playTrack(track, playlist, index);
    });
    
    // 初始化播放器状态，不恢复上次的播放状态
    // 清除之前的播放状态
    localStorage.removeItem('audioPlayerState');
    
    // 定期检查并更新缓冲进度
    setInterval(updateLoadingProgress, 1000);
    
    // 定期保存播放器状态 (每10秒)
    setInterval(() => {
      if (window.audioPlayerState && window.audioPlayerState.currentTrack) {
        savePlayerState();
      }
    }, 10000);
    
    // 更新加载进度条
    function updateLoadingProgress() {
      if (!audioElement) return;
      
      try {
        if (audioElement.buffered.length > 0) {
          const bufferedEnd = audioElement.buffered.end(audioElement.buffered.length - 1);
          const duration = audioElement.duration;
          
          if (duration > 0) {
            const loadProgress = (bufferedEnd / duration) * 100;
            if (loadingProgressBar) loadingProgressBar.style.width = `${loadProgress}%`;
            
            // 更新移动端加载进度条
            const mobileLoadingProgressBar = document.getElementById('mobile-loading-progress-bar');
            const mobileLoadingProgressBar2 = document.getElementById('mobile-loading-progress-bar-2');
            
            if (mobileLoadingProgressBar) {
              mobileLoadingProgressBar.style.width = `${loadProgress}%`;
            }
            
            if (mobileLoadingProgressBar2) {
              mobileLoadingProgressBar2.style.width = `${loadProgress}%`;
            }
          }
        }
      } catch (e) {
        console.error("Error updating loading progress:", e);
      }
    }
    
    // 更新播放器信息
    function updatePlayerInfo(track) {
      if (!track) return;
      
      console.log('Updating player info with track:', track);
      
      // 更新桌面端的标题和艺术家信息
      if (playerTitle) playerTitle.textContent = track.name || 'Unknown Title';
      if (playerArtist) playerArtist.textContent = track.artist || 'Unknown Artist';
      
      if (playerTitleDesktop) playerTitleDesktop.textContent = track.name || 'Unknown Title';
      if (playerArtistDesktop) playerArtistDesktop.textContent = track.artist || 'Unknown Artist';
      
      // 更新移动端的标题和艺术家信息
      if (playerTitleMobile) playerTitleMobile.textContent = track.name || 'Unknown Title';
      if (playerArtistMobile) playerArtistMobile.textContent = track.artist || 'Unknown Artist';
      
      // 更新播放历史
      addToPlayHistory(track);
      
      // 触发播放器信息更新事件
      document.dispatchEvent(new CustomEvent('player-info-updated', {
        detail: { track }
      }));
    }
  });
</script>

<script is:inline>
  // 播放历史管理服务
  const MAX_HISTORY_ITEMS = 20;
  
  // 获取播放历史
  function getPlayHistory() {
    try {
      const historyJson = localStorage.getItem('playHistory');
      return historyJson ? JSON.parse(historyJson) : [];
    } catch (error) {
      return [];
    }
  }
  
  // 添加曲目到播放历史
  function addToPlayHistory(track) {
    try {
      if (!track || !track.name) return;
      
      // 获取当前历史
      let history = getPlayHistory();
      
      // 移除相同曲目（如果存在）
      history = history.filter(item => {
        // 检查曲目名称
        if (item.name !== track.name) return true;
        
        // 检查艺术家字段，处理可能的不同类型
        if (item.artist === track.artist) return false;
        
        // 如果两者都有 artists 字段，则比较
        if (item.artists && track.artists) {
          // 如果两者都是数组，使用 JSON.stringify 比较
          if (Array.isArray(item.artists) && Array.isArray(track.artists)) {
            return JSON.stringify(item.artists) !== JSON.stringify(track.artists);
          }
          // 如果两者都是字符串，直接比较
          if (typeof item.artists === 'string' && typeof track.artists === 'string') {
            return item.artists !== track.artists;
          }
        }
        
        return true; // 如果无法确定是否相同，则保留该曲目
      });
      
      // 确保track对象包含所有必要字段
      const trackToAdd = {
        ...track,
        id: track.id || `track-${Date.now()}`, // 确保有ID
        title: track.title || track.name, // 确保有title字段
        name: track.name || track.title, // 确保有name字段
        album: track.album || '',
        artist: track.artist || (track.artists ? (Array.isArray(track.artists) ? track.artists.join(', ') : track.artists) : ''), // 确保保存 artist 字段
        playedAt: new Date().toISOString(),
        url: track.url || track.audioUrl,
        audioUrl: track.audioUrl || track.url,
        status: track.status || 'Unknown' // 确保有status字段
      };
      
      // 添加到历史开头
      history.unshift(trackToAdd);
      
      // 限制历史长度
      if (history.length > MAX_HISTORY_ITEMS) {
        history = history.slice(0, MAX_HISTORY_ITEMS);
      }
      
      // 保存到本地存储
      try {
        localStorage.setItem('playHistory', JSON.stringify(history));
      } catch (storageError) {
        // 忽略存储错误
      }
      
      // 触发历史更新事件
      try {
        const event = new CustomEvent('play-history-updated', {
          detail: { history }
        });
        document.dispatchEvent(event);
      } catch (eventError) {
        // 忽略事件错误
      }
      
      return history;
    } catch (error) {
      return [];
    }
  }
  
  // 清除播放历史
  function clearPlayHistory() {
    try {
      localStorage.removeItem('playHistory');
      
      // 触发历史更新事件
      const event = new CustomEvent('play-history-updated', {
        detail: { history: [] }
      });
      document.dispatchEvent(event);
    } catch (error) {
      // 忽略清除历史错误
    }
  }
  
  // 保存播放器状态
  // 防止无限循环的标志
  let isSavingPlayerState = false;
  
  function savePlayerState() {
    // 如果已经在保存状态中，直接返回，防止无限循环
    if (isSavingPlayerState) return;
    
    try {
      // 设置标志，表示正在保存状态
      isSavingPlayerState = true;
      
      if (window.audioPlayerState && window.audioPlayerState.currentTrack) {
        // 确保所有必要的字段都存在并且有效
        const currentTrack = { ...window.audioPlayerState.currentTrack };
        
        // 确保 URL 字段存在
        if (!currentTrack.url && currentTrack.audioUrl) {
          currentTrack.url = currentTrack.audioUrl;
        } else if (!currentTrack.audioUrl && currentTrack.url) {
          currentTrack.audioUrl = currentTrack.url;
        }
        
        // 确保有 ID
        if (!currentTrack.id) {
          currentTrack.id = `track-${Date.now()}`;
        }
        
        const state = {
          currentTrack: currentTrack,
          playlist: window.audioPlayerState.playlist || [],
          currentIndex: window.audioPlayerState.currentIndex || 0,
          currentTime: document.getElementById('audio-element')?.currentTime || 0,
          isPlaying: window.audioPlayerState.isPlaying,
          timestamp: Date.now() // 添加时间戳以跟踪保存时间
        };
        
        // 保存到 localStorage
        localStorage.setItem('audioPlayerState', JSON.stringify(state));
        
        // 触发播放状态变化事件，通知其他组件当前正在播放的曲目
        if (state.currentTrack && state.currentTrack.id) {
          const stateChangeEvent = new CustomEvent('audio-player-state-change', { 
            detail: { state: state }
          });
          document.dispatchEvent(stateChangeEvent);
          
          // 使用自定义事件通知 FeaturedTracks 组件更新按钮状态
          const updateButtonEvent = new CustomEvent('update-play-buttons', { 
            detail: { 
              trackId: state.currentTrack.id,
              isPlaying: state.isPlaying
            } 
          });
          document.dispatchEvent(updateButtonEvent);
        }
      }
    } catch (error) {
      // 忽略保存状态错误
    } finally {
      // 无论成功还是失败，都重置标志
      isSavingPlayerState = false;
    }
  }
  
  // 恢复播放器状态
  function restorePlayerState() {
    try {
      const stateJson = localStorage.getItem('audioPlayerState');
      if (!stateJson) return null;
      
      const state = JSON.parse(stateJson);
      
      // 验证状态是否有效
      if (!state.currentTrack || (!state.currentTrack.url && !state.currentTrack.audioUrl)) {
        return null;
      }
      
      // 确保 URL 字段存在
      if (!state.currentTrack.url && state.currentTrack.audioUrl) {
        state.currentTrack.url = state.currentTrack.audioUrl;
      } else if (!state.currentTrack.audioUrl && state.currentTrack.url) {
        state.currentTrack.audioUrl = state.currentTrack.url;
      }
      
      // 确保有 ID
      if (!state.currentTrack.id) {
        state.currentTrack.id = `track-${Date.now()}`;
      }
      
      return state;
    } catch (error) {
      return null;
    }
  }
  
  // 暴露给全局
  window.getPlayHistory = getPlayHistory;
  window.addToPlayHistory = addToPlayHistory;
  window.clearPlayHistory = clearPlayHistory;
  window.savePlayerState = savePlayerState;
  window.restorePlayerState = restorePlayerState;
  
  // 初始化全局音频播放状态
  window.audioPlayerState = {
    currentTrack: null,
    playlist: [],
    isPlaying: false,
    currentIndex: -1
  };
</script>
