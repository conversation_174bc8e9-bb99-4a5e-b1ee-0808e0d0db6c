---
// src/components/categoryViews/AlbumGridDisplay.astro
import ArtworkImage from '../ArtworkImage.astro';

// 定义 Props
export interface Props {
  albums: Array<{
    id: string;
    name: string;
    coverImage: string;
    backgroundColor: string;
    trackCount: number;
    hasData?: boolean;
    // 其他可能的字段...
  }>;
  categoryId: string;
  artistId: string;
}

const { albums, categoryId, artistId } = Astro.props;

// 确定首屏图片（前8个）
const getImagePriority = (index) => index < 8 ? "high" : "low";
---

<section>
  <h2 class="text-xl font-bold text-white mb-4">Album Eras ({albums.length})</h2>
  {albums && albums.length > 0 ? (
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
      {albums.map((era, index) => {
        const hasData = era.hasData !== false; // 默认为 true，除非明确设置为 false
        return (
          <a href={`/artists/${artistId}/${categoryId}/eras/${era.id}`} class="group">
            <div
              class={`aspect-square rounded-lg overflow-hidden relative transition-all duration-300 ease-in-out ${
                hasData
                  ? 'group-hover:shadow-lg group-hover:scale-105 ring-1 ring-primary/20 border border-primary/30'
                  : 'opacity-60 group-hover:opacity-80 border border-gray-600'
              }`}
              style={`background-color: ${era.backgroundColor || '#333'}`}
            >
            <ArtworkImage 
              artwork={{
                title: era.name,
                imageUrl: era.coverImage,
                url: era.coverImage
              }}
              priority={getImagePriority(index)}
              className="w-full h-full"
            />
            {/* 始终显示的标题和曲目数量 */}
            <div class="absolute inset-0 flex items-end p-2 sm:p-3">
              <div class={`z-10 ${hasData ? 'text-white' : 'text-gray-400'}`}>
                <h3 class={`font-bold text-sm sm:text-base leading-tight line-clamp-2 drop-shadow-md ${hasData ? '' : 'text-gray-400'}`}>
                  {era.name}
                  {hasData && <span class="ml-1 text-primary text-xs">●</span>}
                </h3>
                <p class={`text-xs sm:text-sm drop-shadow-md ${hasData ? 'text-gray-300' : 'text-gray-500'}`}>{era.trackCount} tracks</p>
              </div>
            </div>
            
            {/* 悬停时的背景遮罩，增强可读性 */}
            <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
          </div>
        </a>
        );
      })}
    </div>
  ) : (
     <div class="text-center py-8">
        <p class="text-gray-400">No albums found in this category.</p>
        <p class="text-gray-500 text-sm mt-2">There are currently no albums to display here.</p>
      </div>
  )}
</section>
