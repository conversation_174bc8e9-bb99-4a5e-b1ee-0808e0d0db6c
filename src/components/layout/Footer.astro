---
import { t, getLanguageFromURL } from '../../i18n/index.js';

// 获取当前语言
let currentLang = getLanguageFromURL(Astro.request.url);

// 检查 URL 参数中的语言设置
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}

const currentYear = new Date().getFullYear();

// 接收视图类型参数
const { view = "responsive" } = Astro.props;
---

<footer class="border-t border-dark-hover bg-black py-6 lg:py-10 w-full" style="padding-bottom: 120px;">
  <!-- 桌面端样式 (lg及以上) -->
  <div class="hidden lg:block max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" style="padding-left: 244px;">
    <div class="flex flex-col md:flex-row justify-between mb-8">
      <div class="mb-8 md:mb-0 md:w-1/3">
        <div class="flex items-center mb-4">
          <svg class="w-8 h-8 text-primary mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
            <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
            <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
          </svg>
          <h3 class="text-primary font-bold text-lg">AITrackerHive</h3>
        </div>
        <p class="text-text-secondary text-base mb-6 max-w-md">
          {t('common.explore', currentLang)}
        </p>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-3 gap-8 md:w-2/3">
        <div class="p-4">
          <h3 class="font-semibold text-white mb-4 text-sm uppercase tracking-wider">{t('navigation.title', currentLang)}</h3>
          <ul class="space-y-3">
            <li><a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="text-text-secondary hover:text-white text-sm transition-colors">{t('navigation.home', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists' : `/${currentLang}/artists`} class="text-text-secondary hover:text-white text-sm transition-colors">{t('navigation.artists', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists/ye/unreleased' : `/${currentLang}/artists/ye/unreleased`} class="text-text-secondary hover:text-white text-sm transition-colors">{t('navigation.albums', currentLang)}</a></li>
          </ul>
        </div>

        <div class="p-4">
          <h3 class="font-semibold text-white mb-4 text-sm uppercase tracking-wider">{t('resources.title', currentLang)}</h3>
          <ul class="space-y-3">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a href="mailto:<EMAIL>" class="text-text-secondary hover:text-white text-sm transition-colors"><EMAIL></a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="border-t border-dark-hover pt-6">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-base text-text-secondary mb-4 md:mb-0">
          {t('footer.copyright', currentLang)}
        </p>
      </div>
    </div>
  </div>

  <!-- 移动端和平板样式 (lg以下) -->
  <div class="block lg:hidden max-w-7xl mx-auto px-4">
    <div class="flex flex-col mb-6">
      <div class="mb-6">
        <div class="flex items-center mb-3">
          <svg class="w-6 h-6 text-primary mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
            <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
            <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
          </svg>
          <h3 class="text-primary font-bold text-base">AITrackerHive</h3>
        </div>
        <p class="text-text-secondary text-sm mb-4 max-w-md">
          {t('common.explore', currentLang)}
        </p>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div class="p-3">
          <h3 class="font-semibold text-white mb-3 text-xs uppercase tracking-wider">{t('navigation.title', currentLang)}</h3>
          <ul class="space-y-2">
            <li><a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="text-text-secondary hover:text-white text-xs transition-colors">{t('navigation.home', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists' : `/${currentLang}/artists`} class="text-text-secondary hover:text-white text-xs transition-colors">{t('navigation.artists', currentLang)}</a></li>
            <li><a href={currentLang === 'en' ? '/artists/ye/unreleased' : `/${currentLang}/artists/ye/unreleased`} class="text-text-secondary hover:text-white text-xs transition-colors">{t('navigation.albums', currentLang)}</a></li>
          </ul>
        </div>

        <div class="p-3">
          <h3 class="font-semibold text-white mb-3 text-xs uppercase tracking-wider">{t('resources.title', currentLang)}</h3>
          <ul class="space-y-2">
            <li class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a href="mailto:<EMAIL>" class="text-text-secondary hover:text-white text-sm transition-colors"><EMAIL></a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="border-t border-dark-hover pt-4">
      <p class="text-sm text-text-secondary text-center">
        {t('footer.copyright', currentLang)}
      </p>
    </div>
  </div>
</footer>
