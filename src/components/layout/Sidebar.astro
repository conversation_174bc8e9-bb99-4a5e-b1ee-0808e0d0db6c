---
// 侧边栏导航组件，参考Spotify风格
import { t, getLanguageFromURL } from '../../i18n/index.js';

// 获取当前语言
let currentLang = getLanguageFromURL(Astro.request.url);

// 检查 URL 参数中的语言设置
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}

// 获取当前路径，用于确定激活的导航项
const pathname = new URL(Astro.request.url).pathname;
const isHome = pathname === '/' || pathname === `/${currentLang}/` || pathname === `/${currentLang}`;
const isArtists = pathname.includes('/artists');
---

<aside class="fixed top-0 left-0 w-60 h-full bg-black z-10 overflow-y-auto sm:block hidden">
  <div class="flex flex-col h-full py-4">
    <!-- Logo -->
    <div class="px-6 py-4 mb-4">
      <a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="flex items-center group">
        <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform">
          <svg class="w-6 h-6 text-black" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
            <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
            <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
          </svg>
        </div>
        <span class="text-white font-bold text-xl ml-3 group-hover:text-primary transition-colors">AITrackerHive</span>
      </a>
    </div>
    
    <!-- Main Navigation -->
    <nav class="mt-2 px-3">
      <ul class="space-y-2">
        <li>
          <a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class={`flex items-center px-4 py-3.5 rounded-xl transition-all duration-300 shadow-md group ${isHome ? 'text-white font-medium bg-gradient-to-r from-[#111111] to-[#0a0a0a]' : 'text-text-secondary hover:text-white hover:bg-gradient-to-r hover:from-[#111111] hover:to-[#0a0a0a]'}`}>
            <div class={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors ${isHome ? 'bg-primary/20' : 'bg-[#0a0a0a] group-hover:bg-primary/20'}`}>
              <svg xmlns="http://www.w3.org/2000/svg" class={`h-4.5 w-4.5 transition-colors ${isHome ? 'text-primary' : 'text-text-secondary group-hover:text-primary'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            </div>
            <span>{t('navigation.home', currentLang)}</span>
          </a>
        </li>

        <li>
          <a href={currentLang === 'en' ? '/artists' : `/${currentLang}/artists`} class={`flex items-center px-4 py-3.5 rounded-xl transition-all duration-300 group ${isArtists ? 'text-white font-medium bg-gradient-to-r from-[#111111] to-[#0a0a0a]' : 'text-text-secondary hover:text-white hover:bg-gradient-to-r hover:from-[#111111] hover:to-[#0a0a0a]'}`}>
            <div class={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-colors ${isArtists ? 'bg-primary/20' : 'bg-[#0a0a0a] group-hover:bg-primary/20'}`}>
              <svg xmlns="http://www.w3.org/2000/svg" class={`h-4.5 w-4.5 transition-colors ${isArtists ? 'text-primary' : 'text-text-secondary group-hover:text-primary'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <span>{t('navigation.artists', currentLang)}</span>
          </a>
        </li>
      </ul>
    </nav>
    
    <!-- Recently Played -->
    <div class="mt-6 px-3">
      <div class="flex items-center justify-between px-4 mb-3">
        <div class="text-text-secondary text-xs font-bold uppercase tracking-wider">{t('common.recentlyPlayed', currentLang)}</div>
        <a href={currentLang === 'en' ? '/#recently-played-section' : `/${currentLang}/#recently-played-section`} class="text-xs text-primary hover:text-primary-hover">{t('common.seeAll', currentLang)}</a>
      </div>
      <div id="sidebar-recent-tracks">
        <div class="text-center py-4 text-text-secondary text-sm hidden" id="sidebar-no-tracks">
          <p>{t('common.noRecentTracks', currentLang)}</p>
        </div>
        <ul class="space-y-2" id="sidebar-recent-tracks-list"></ul>
      </div>
    </div>
    
    <script>
      // 在侧边栏中显示最近播放的曲目
      function updateSidebarRecentlyPlayed() {
        // 从localStorage获取播放历史
        let playHistory = [];
        try {
          const historyJson = localStorage.getItem('playHistory');
          playHistory = historyJson ? JSON.parse(historyJson) : [];
        } catch (error) {
          console.error('Error getting play history for sidebar:', error);
        }
        
        const tracksList = document.getElementById('sidebar-recent-tracks-list');
        const noTracksEl = document.getElementById('sidebar-no-tracks');
        
        // 如果没有最近播放记录，显示空状态
        if (!playHistory || playHistory.length === 0) {
          noTracksEl.classList.remove('hidden');
          tracksList.classList.add('hidden');
          return;
        }
        
        // 否则显示列表
        noTracksEl.classList.add('hidden');
        tracksList.classList.remove('hidden');
        
        // 清空列表
        tracksList.innerHTML = '';
        
        // 只显示前8个最近播放的曲目
        const recentTracks = playHistory.slice(0, 8);
        
        // 填充列表
        recentTracks.forEach((track) => {
          const li = document.createElement('li');
          
          // 创建点击事件处理函数
          li.addEventListener('click', function() {
            // 1. 跳转到首页的Recently Played部分
            // 获取当前语言
            const currentLang = document.documentElement.lang || 'en';
            const langPrefix = currentLang === 'en' ? '' : `/${currentLang}`;
            window.location.href = `${langPrefix}/#recently-played-section`;
            
            // 2. 触发播放事件
            setTimeout(() => {
              const playEvent = new CustomEvent('play-track', {
                detail: {
                  track: track,
                  playlist: [track],
                  index: 0
                }
              });
              document.dispatchEvent(playEvent);
            }, 100); // 短暂延迟确保页面已加载
          });
          
          li.className = 'cursor-pointer';
          li.innerHTML = `
            <div class="flex items-center p-2 rounded-lg hover:bg-[#111111] transition-colors group">
              <div class="w-10 h-10 rounded overflow-hidden bg-[#0a0a0a] flex-shrink-0 relative">
                <img 
                  src="${track.albumCover || '/images/album-placeholder.svg'}" 
                  alt="${track.name || 'Unknown Track'}"
                  class="w-full h-full object-cover"
                  onerror="this.onerror=null; this.style.display='none'; this.parentNode.querySelector('.track-placeholder').style.display='flex';"
                />
                <div class="track-placeholder w-full h-full hidden items-center justify-center bg-[#0a0a0a] text-text-secondary">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                </div>
              </div>
              <div class="ml-3 overflow-hidden">
                <p class="text-sm font-medium text-white truncate group-hover:text-primary transition-colors">${track.name || 'Unknown Track'}</p>
                <p class="text-xs text-text-secondary truncate">${track.artists ? (Array.isArray(track.artists) ? track.artists.join(', ') : track.artists) : '-'}</p>
              </div>
            </div>
          `;
          
          tracksList.appendChild(li);
        });
      }
      
      // 页面加载时更新侧边栏最近播放列表
      document.addEventListener('DOMContentLoaded', updateSidebarRecentlyPlayed);
      
      // 监听播放事件，更新侧边栏最近播放列表
      document.addEventListener('play-track', () => {
        // 短暂延迟确保localStorage已更新
        setTimeout(updateSidebarRecentlyPlayed, 100);
      });
      
      // 监听播放历史更新事件（包括清除历史记录）
      document.addEventListener('play-history-updated', () => {
        updateSidebarRecentlyPlayed();
      });
    </script>
    
    <!-- 版权信息 -->
    <div class="mt-auto px-6 py-4 text-center">
      <p class="text-xs text-text-secondary">{t('footer.copyright', currentLang)}</p>
    </div>
  </div>
</aside>
