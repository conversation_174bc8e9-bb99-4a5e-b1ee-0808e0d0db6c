---
// 精选曲目组件 - 显示随机选择的曲目
import { Image } from 'astro:assets';

// 定义 Props 接口
export interface Props {
  title?: string;
}

const { title = "Latest Updates" } = Astro.props;

// 从 JSON 文件中获取数据
import fs from 'fs';
import path from 'path';

let allTracks = [];

try {
  // 使用 path.join 构建绝对路径
  const featuredTracksPath = path.join(process.cwd(), 'public', 'data', 'featured-tracks.json');
  
  // 读取文件内容
  const fileContent = fs.readFileSync(featuredTracksPath, 'utf-8');
  
  // 解析 JSON 数据
  allTracks = JSON.parse(fileContent);
} catch (error) {
  console.error('Error loading featured tracks:', error);
}

// 注意：我们不再在服务器端随机选择曲目
// 而是将所有曲目传递给客户端，由客户端进行随机选择
---

<section class="mt-12 mb-16">
  <div class="flex items-center mb-6">
    <h2 class="text-2xl font-bold text-white">{title}</h2>
  </div>
  
  <div id="featured-tracks-container" class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <!-- 这里的内容将由客户端JavaScript动态生成 -->
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // 获取所有曲目数据
    fetch('/data/featured-tracks.json')
      .then(response => response.json())
      .then(allTracks => {
        // 随机选择4个曲目
        const featuredTracks = getRandomTracks(allTracks, 4);
        
        // 渲染到页面
        renderFeaturedTracks(featuredTracks);
        
        // 设置播放按钮事件
        setupPlayButtons();
      })
      .catch(error => {
        console.error('Error loading featured tracks:', error);
      });
    
    // 随机选择曲目函数
    function getRandomTracks(tracks, count) {
      const shuffled = [...tracks].sort(() => 0.5 - Math.random());
      return shuffled.slice(0, count);
    }
    
    // 渲染曲目到页面
    function renderFeaturedTracks(tracks) {
      const container = document.getElementById('featured-tracks-container');
      if (!container) return;
      
      container.innerHTML = tracks.map(track => `
        <div class="track-card bg-dark-elevated rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 hover:transform hover:scale-[1.02] group">
          <div class="block">
            <div class="relative aspect-square">
              <img
                src="${track.albumCover || '/images/album-placeholder.svg'}"
                alt="${track.title}"
                class="w-full h-full object-cover"
                loading="lazy"
                decoding="async"
                fetchpriority="low"
                onerror="this.onerror=null; this.src='/images/album-placeholder.svg';"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-90"></div>
              
              <!-- Play 按钮 - 居中 -->
              <button
                class="play-button absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-primary/80 hover:bg-primary text-black rounded-full w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center transition-all duration-200 z-20 backdrop-blur-sm shadow-lg"
                data-track-id="${track.id}"
                data-track-title="${track.title}"
                data-track-artist="${track.artists || 'Ye'}"
                data-track-cover="${track.albumCover || '/images/album-placeholder.svg'}"
                data-track-url="${track.url}"
              >
                <!-- 播放图标 -->
                <svg xmlns="http://www.w3.org/2000/svg" class="play-icon h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <!-- 暂停图标 -->
                <svg xmlns="http://www.w3.org/2000/svg" class="pause-icon h-4 w-4 sm:h-5 sm:w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>

              <!-- 下载按钮 - 右下角，直接显示 -->
              <div class="download-button-container track-download absolute bottom-3 right-3 z-20"
                   data-track-id="${track.id}"
                   data-track-title="${track.title}"
                   data-download-url="${track.url}"
                   data-track-audio="${track.url}"
                   data-track-original-url="${track.originalUrl || ''}"
                   data-track-original-content='${JSON.stringify(track.originalContent || {}).replace(/'/g, "&apos;")}'>
                <button class="download-btn bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-gray-100 hover:text-white rounded-lg w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-gray-500/40 hover:border-gray-400/60"
                        data-action="download"
                        title="Download track">
                  <svg class="download-icon w-4 h-4 sm:w-4.5 sm:h-4.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                  </svg>
                </button>
              </div>

            </div>
            <div class="p-2 sm:p-3">
              <h3 class="font-bold text-white text-sm sm:text-lg mb-0.5 line-clamp-1">${track.title}</h3>
              <p class="text-xs sm:text-sm text-gray-200 font-medium mb-0.5 sm:mb-1 line-clamp-1">${track.artists || 'Ye'}</p>
              <p class="text-xs text-gray-400 line-clamp-1 sm:line-clamp-2 sm:block">${track.era}</p>
            </div>
          </div>
        </div>
      `).join('');
    }
    
    // 设置播放按钮事件
    function setupPlayButtons() {
      // 获取所有播放按钮
      const playButtons = document.querySelectorAll('.play-button');
      
      // 更新播放状态函数 - 显示正在播放的曲目
      function updatePlayingState(currentTrackId) {
        // 重置所有按钮状态
        playButtons.forEach(button => {
          const buttonId = button.getAttribute('data-track-id');
          const playIcon = button.querySelector('.play-icon');
          const pauseIcon = button.querySelector('.pause-icon');
          
          if (buttonId === currentTrackId) {
            // 当前播放的曲目
            playIcon.classList.add('hidden');
            pauseIcon.classList.remove('hidden');
            button.classList.add('bg-primary-dark');
            button.classList.remove('bg-primary');
          } else {
            // 其他曲目
            playIcon.classList.remove('hidden');
            pauseIcon.classList.add('hidden');
            button.classList.remove('bg-primary-dark');
            button.classList.add('bg-primary');
          }
        });
      }
      
      // 为每个播放按钮添加点击事件
      playButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          
          const trackId = button.getAttribute('data-track-id');
          const trackTitle = button.getAttribute('data-track-title');
          const trackArtist = button.getAttribute('data-track-artist');
          const trackCover = button.getAttribute('data-track-cover');
          const trackUrl = button.getAttribute('data-track-url');
          
          // 创建一个事件，通知底部播放器播放这个曲目
          const trackData = {
            id: trackId,
            name: trackTitle,
            artists: trackArtist,
            albumCover: trackCover,
            url: trackUrl // 使用 JSON 中的 url 字段
          };
          
          // 如果存在播放历史函数，添加到播放历史
          if (typeof window.addToPlayHistory === 'function') {
            window.addToPlayHistory(trackData);
          }
          
          // 触发自定义事件，通知底部播放器播放这个曲目
          const playEvent = new CustomEvent('play-track', { 
            detail: { 
              track: trackData,
              playlist: [trackData],
              index: 0
            } 
          });
          document.dispatchEvent(playEvent);
          
          // 更新播放状态
          updatePlayingState(trackId);
          
          console.log(`播放曲目: ${trackTitle} - ${trackArtist}, URL: ${trackUrl}`);
        });
      });
      
      // 监听播放状态变化 - 使用新的事件名称避免循环
      document.addEventListener('update-play-buttons', (e) => {
        if (e.detail && e.detail.trackId) {
          updatePlayingState(e.detail.trackId);
        }
      });
      
      // 初始化时检查当前播放状态
      if (window.audioPlayerState && window.audioPlayerState.currentTrack) {
        updatePlayingState(window.audioPlayerState.currentTrack.id);
      }
    }
  });
</script>
