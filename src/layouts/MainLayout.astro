---
import Layout from './Layout.astro';
import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';
import Sidebar from '../components/layout/Sidebar.astro';
import AudioPlayer from '../components/AudioPlayer.astro';
import ArtworkModal from '../components/ArtworkModal.astro';
import DeviceDetection from '../components/DeviceDetection.astro';
import DownloadModal from '../components/DownloadModal.astro';


interface Props {
  title: string;
  description?: string;
  canonicalUrl?: string;
  ogImage?: string;
  type?: 'website' | 'article' | 'music';
  jsonLd?: object;
}

const { title, description, canonicalUrl, ogImage, type, jsonLd } = Astro.props;
---

<Layout 
  title={title}
  description={description}
  canonicalUrl={canonicalUrl}
  ogImage={ogImage}
  type={type}
  jsonLd={jsonLd}
>
  <DeviceDetection />
  
  <div class="relative flex min-h-screen flex-col bg-black">
    <div class="flex flex-1">
      <div class="th-desktop-view">
        <Sidebar />
      </div>
      <div class="flex-1 flex flex-col w-full th-desktop-view:ml-60">
        <Header />
        <main class="flex-1 bg-gradient-to-b from-[#0e0e0e] to-black px-4 py-6 th-mobile-view:py-2 th-mobile-view:pt-4 th-mobile-view:pb-20">
          <div class="max-w-7xl mx-auto w-full">
            <slot />
          </div>
        </main>

        <!-- Adsterra Native Banner Ad - 全局显示 -->
        <section class="mb-8 md:mb-12 px-4">
          <div class="max-w-7xl mx-auto">
            <script
              async="async"
              data-cfasync="false"
              src="//pl26439313.profitableratecpm.com/74a0e680dd67acebca5885726364cfc5/invoke.js"
            ></script>
            <div id="container-74a0e680dd67acebca5885726364cfc5"></div>
          </div>
        </section>

        <Footer view="responsive" />
      </div>
    </div>
  </div>
  
  <AudioPlayer />
  <ArtworkModal />
  <DownloadModal />

  <!-- 新的下载功能脚本 -->
  <script src="/utils/newDownloader.js" is:inline></script>
</Layout>

<style>
  /* 全局背景色统一 */
  :global(body) {
    background-color: #000000;
  }
  
  /* 为桌面视图添加侧边栏边距 */
  @media (min-width: 1024px) {
    .th-desktop-view\:ml-60 {
      margin-left: 240px;
    }
  }
</style>
